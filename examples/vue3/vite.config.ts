import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import vueDevTools from 'vite-plugin-vue-devtools';

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  const plugins = [vue(), vueDevTools()];

  // 只在生产环境下添加 html-transform 插件
  if (command === "build") {
    plugins.push({
      name: "html-transform",
      transformIndexHtml(html) {
        // Add trtc.js script tag before first script
        html = html.replace(
          "<script",
          '<script src="init-trtc.js"></script><script'
        );
        return html;
      },
    });
  }
  return {
    plugins,
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    server: {
      proxy: {
        "^/(esbuild|plugins|static|trtc\\.js(.map)?)": {
          target: "http://localhost:9988",
          changeOrigin: true,
        },
      },
    },
    base: "./",
    build: {
      sourcemap: true,
      rollupOptions: {
        external: ["trtc-sdk-v5"],
        output: {
          format: "iife",
          globals: {
            "trtc-sdk-v5": "TRTC",
          },
          entryFileNames: "assets/[name].js",
          chunkFileNames: "assets/[name].js",
          assetFileNames: "assets/[name].[ext]",
        },
      },
    },
  };
});
