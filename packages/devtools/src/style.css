#trtc-devtools2-main {
	position: fixed;
	left: 50%;
	transform: translateX(-50%);
	bottom: 20px;
	pointer-events: auto;
	max-width: 1200px;
	color: #333;
	z-index: 2147483646;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 90%;
}

#panel {
	width: 100%;
	height: 500px;
	transition: all 0.3s ease;
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
	border: 1px solid rgba(0, 0, 0, 0.1);
	margin-bottom: -8px;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
}

.toolbar {
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 8px;
	pointer-events: auto;
	display: inline-flex;
	align-items: center;
	padding: 0 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
	border: 1px solid rgba(0, 0, 0, 0.1);
	width: fit-content;
}

.toolbar button {
	background: none;
	border: none;
	padding: 0;
	margin: 0;
	cursor: pointer;
	border-radius: 4px;
	transition: all 0.2s ease;
	display: flex; /* 使用 flex 布局确保图片垂直居中 */
	align-items: center;
	justify-content: center;
}

.toolbar button:hover {
	background-color: rgba(0, 0, 0, 0.05);
}

.toolbar button img {
  filter: brightness(0) saturate(100%) opacity(0.7); /* 将SVG图标转为深灰色 */
	padding: 6px;
	display: block; /* 移除行内元素的基线对齐 */
}

.toolbar button:hover img {
	filter: brightness(0) saturate(100%) opacity(0.9);
}

.logo {
  width: 32px;
  height: 32px;
	margin: -6px;
	cursor: pointer;
  filter: none !important; /* 保持logo原样，不应用白色滤镜 */
	display: block; /* 移除行内元素的基线对齐 */
}

.logo:hover {
	filter: none !important; /* 保持logo原样，不应用白色滤镜 */
}

.network-check {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.security-check {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.security-status {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.security-message {
  font-size: 14px;
  color: #495057;
  line-height: 1.5;
}

.message-line {
  margin: 4px 0;
}

.network-status, .security-status {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
}

.status-indicator.online {
  background-color: #28a745;
}

.status-indicator.offline {
  background-color: #dc3545;
}

.status-indicator.warning {
  background-color: #ffc107;
}

.status-text {
  font-size: 16px;
  color: #212529;
}

.sites-status {
  margin: 20px 0;
}

.site-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #dee2e6;
}

.site-name {
  font-weight: 500;
}

.site-status {
  font-size: 14px;
}

.site-status.online {
  color: #28a745;
}

.site-status.offline {
  color: #dc3545;
}

.network-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.last-check {
  color: #6c757d;
  font-size: 14px;
}

.check-button {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.check-button:hover {
  background-color: #0056b3;
}

.check-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.card {
  display: flex;
  gap: 10px;
  width: 100%;
}

.card-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
  border: 1px solid #9ca3af33;
  flex: 1;
}

.panel-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
}

.tab-container {
  display: flex;
  align-items: flex-end;
  height: 100%;
}

button {
  border: none;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

button:hover {
  background-color: #e5e7eb;
}

button:active {
  background-color: #d1d5db;
  transform: translateY(1px);
}

/* Tab button styles */
.tab-button {
  border: none;
  background-color: transparent;
  border-radius: 0;
  padding: 12px 16px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  border-bottom: 2px solid transparent;
  margin: 0;
  min-width: 80px;
  text-align: center;
}

.tab-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #374151;
  transform: none;
}

.tab-button:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: none;
}

.tab-button.active {
  color: #2563eb;
  background-color: white;
  border-bottom: 2px solid #2563eb;
  font-weight: 500;
}

.tab-button.active:hover {
  background-color: white;
  color: #2563eb;
}

/* 第一个和最后一个 tab 的圆角处理 */
.tab-button:first-child {
  border-top-left-radius: 8px;
}

.tab-button:last-child {
  border-top-right-radius: 8px;
}

.room-info {
  display: flex;
  flex-direction: row;
  gap: 10px;
  width: 100%;
}

.room-info-item {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.room-info-label {
  color: #333;
  margin-right: 10px;
  opacity: 0.5;
}

.room-info-value-clickable {
  cursor: pointer;
  text-decoration: underline;
  text-decoration-style: dashed;
}

/* 抽屉蒙层 */
.drawer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  pointer-events: none;
  transition: background-color 0.3s ease;
  z-index: 9;
  border-radius: 8px;
}

.drawer-overlay.show {
  background-color: rgba(0, 0, 0, 0.3);
  pointer-events: auto;
}

.drawer {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 100%;
  background-color: white;
  border-left: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  display: flex;
  flex-direction: column;
  border-radius: 8px;
}

.drawer.open {
  width: 300px;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
}

.drawer-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.drawer-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.drawer-close {
  background: none;
  border: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.drawer-close:hover {
  background-color: #e5e7eb;
  color: #374151;
}

.drawer-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 插件列表样式 */
.plugin-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.plugin-item {
  background-color: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px 16px;
  transition: all 0.2s ease;
}

.plugin-item:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.plugin-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}