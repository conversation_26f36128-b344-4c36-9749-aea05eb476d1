import graph from '../assets/graph.svg';
import functionIcon from '../assets/function.svg';
import netIcon from '../assets/net.svg';
import charBarIcon from '../assets/char-bar.svg';
import cameraQuestionIcon from '../assets/camera-question.svg';
import settingsIcon from '../assets/settings.svg';
import TRTCLogo from '../assets/trtc.io-logo-100-100-compressed.png';

interface ToolbarProps {
  onTogglePanel: () => void;
}

export default function Toolbar({ onTogglePanel }: ToolbarProps) {
  return (
    <div className="toolbar">
      <button type="button" title="TRTC DevTools" onClick={onTogglePanel}>
        <img src={TRTCLogo} alt="trtc-logo" className="logo" title="TRTC DevTools" />
      </button>
      <button type="button" title="网络状态">
        <img src={netIcon} alt="net" style={{ width: '28px', height: '28px' }} />
      </button>
      <button type="button" title="数据统计">
        <img src={graph} alt="graph" style={{ width: '24px', height: '24px' }} />
      </button>
      <button type="button" title="功能调试">
        <img src={functionIcon} alt="function" style={{ width: '28px', height: '28px' }} />
      </button>
      <button type="button" title="数据监控">
        <img src={charBarIcon} alt="char-bar" style={{ width: '24px', height: '24px' }} />
      </button>
      <button type="button" title="设备检测">
        <img src={cameraQuestionIcon} alt="camera-question" style={{ width: '24px', height: '24px' }} />
      </button>
      <button type="button" title="设置">
        <img src={settingsIcon} alt="settings" style={{ width: '24px', height: '24px' }} />
      </button>
    </div>
  );
}
