/* eslint-disable no-console */
// packages/devtools/src/index.tsx
import { render } from 'preact';
import { useState, useEffect } from 'preact/hooks';
import Toolbar from './components/Toolbar';
import Aegis from 'aegis-web-sdk';
import './style.css';

function convertState(state: string) {
  switch (state) {
    case 'joined':
      return '已加入';
    case 'left':
      return '已离开';
    case '[*]':
      return '未进房';
    default:
      return '未知';
  }
}

function App() {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isShowDrawer, setIsShowDrawer] = useState(false);
  const [drawerTitle, setDrawerTitle] = useState('');
  const [drawerContent, setDrawerContent] = useState(null);
  const [trtcNameList, setTrtcNameList] = useState([]);
  const [currentName, setCurrentName] = useState('');
  const [showData, setShowData] = useState({});

  useEffect(() => {
    const refreshIntervalId = setInterval(() => {
      if (isExpanded) {
        refresh(false);
      }
    }, 1000);
    return () => {
      clearInterval(refreshIntervalId);
    };
  }, [isExpanded]);

  const refresh = (needLog = true) => {
    const data = (window as any).__TRTC_DEVTOOLS_DATA__;
    const trtcList = Object.keys(data);
    const newData = {};
    for (const name of trtcList) {
      const trtc = data[name];
      const currentData = {
        roomId: trtc?.instance?._room?.roomId || '未知',
        scene: trtc?.instance?._room?.scene || '未知',
        userId: trtc?.instance?._room?.userId || '未知',
        role: trtc?.instance?._room?.role || '未知',
        state: trtc?.instance?._room?._state || '未知',
        sdkAppId: trtc?.instance?._room?.sdkAppId || '未知',
        functions: trtc?.functions || [],
        version: trtc?.instance?._version || '未知',
        pluginList: [...trtc?.instance?._plugins].map(i => i[0])
      };
      if (needLog) console.warn(name, currentData, trtc?.instance);
      newData[name] = currentData;
    }
    setShowData(newData);
    setTrtcNameList(trtcList);
    if (currentName === '') {
      setCurrentName(trtcList[0]);
    }
  };

  const handleTogglePanel = () => {
    setIsExpanded(!isExpanded);
    refresh(true);
  };

  function PluginList() {
    return (
      <div className="plugin-list">
        {showData[currentName].pluginList.map((item, index) => (
          <div key={index} className="plugin-item">
            <span className="plugin-name">{item}</span>
          </div>
        ))}
      </div>
    );
  }

  const showPluginList = () => {
    setDrawerTitle('插件列表');
    setDrawerContent(<PluginList />);
    setIsShowDrawer(true);
  };

  const closeDrawer = () => {
    setIsShowDrawer(false);
  };

  const handleOverlayClick = () => {
    closeDrawer();
  };

  return (
    <>
      {isExpanded && <div id="panel">
        <div className="panel-header">
          <div className="tab-container">
            {trtcNameList.map(item => (
              <button
                key={item}
                className={`tab-button ${currentName === item ? 'active' : ''}`}
                onClick={() => setCurrentName(item)}
              >
                {item}
              </button>
            ))}
          </div>
        </div>
        {
          currentName && (
            <div className="card">

              <div className="card-item">
                <div className="room-info">
                  <div className="room-info-item">
                    <span className="room-info-label">版本</span>
                    <span>{showData[currentName].version}</span>
                  </div>
                  <div className="room-info-item">
                    <span className="room-info-label">插件</span>
                    <span className="room-info-value-clickable" onClick={showPluginList}>{showData[currentName].pluginList.length}</span>
                  </div>
                  <div className="room-info-item">
                    <span className="room-info-label">房间号</span>
                    <span>{showData[currentName].roomId}</span>
                  </div>
                  <div className="room-info-item">
                    <span className="room-info-label">用户ID</span>
                    <span>{showData[currentName].userId}</span>
                  </div>
                  <div className="room-info-item">
                    <span className="room-info-label">应用ID</span>
                    <span>{showData[currentName].sdkAppId}</span>
                  </div>
                  <div className="room-info-item">
                    <span className="room-info-label">场景</span>
                    <span>{showData[currentName].scene}</span>
                  </div>
                  <div className="room-info-item">
                    <span className="room-info-label">角色</span>
                    <span>{showData[currentName].role}</span>
                  </div>
                  <div className="room-info-item">
                    <span className="room-info-label">状态</span>
                    <span>{convertState(showData[currentName].state)}</span>
                  </div>
                </div>
              </div>
            </div>
          )
        }

        <div className={`drawer-overlay ${isShowDrawer ? 'show' : ''}`} onClick={handleOverlayClick}></div>
        <div className={`drawer ${isShowDrawer ? 'open' : ''}`}>
          <div className="drawer-header">
            <h3 className="drawer-title">{drawerTitle}</h3>
            <button className="drawer-close" onClick={closeDrawer}>×</button>
          </div>
          <div className="drawer-content">
            {drawerContent}
          </div>
        </div>
      </div>}

      <Toolbar onTogglePanel={handleTogglePanel} />
    </>
  );
}

function initReport() {
  const aegis = new Aegis({ id: 'iHWefAYqgYMbhmQBIN' });
  aegis.reportEvent({ name: 'init' });
}

export function initDevTools() {
  if (document.getElementById('trtc-devtools2-main')) return;

  const containerElement = document.createElement('div');
  containerElement.id = 'trtc-devtools2-main';
  document.body.appendChild(containerElement);
  initReport();

  render(<App />, containerElement);
}

export default initDevTools;
