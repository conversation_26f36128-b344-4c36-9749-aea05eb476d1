import { ArgumentMap, EventEmitter } from 'eventemitter3';
import {
  AUDIO_LEVEL_SCALE,
  AudioReferenceChangeType,
  BannedReason,
  CaptureOption,
  ConnectionState,
  CoreError,
  ErrorCode as CoreErrorCode,
  CustomMessage,
  CustomMessageData,
  DeviceChangeEvent,
  DeviceInfo,
  ILocalTrack,
  IRemoteTrack,
  IRoom,
  ITrack,
  LOG_LEVEL,
  LocalAudioTrack,
  LocalScreenAudioTrack,
  LocalScreenTrack,
  LocalTrackEvent,
  LocalTrackEventTypes,
  LocalVideoTrack,
  MAX_RTT,
  MediaType,
  NAME,
  NetworkQuality,
  PlayOption,
  PublishedUser,
  RemoteAudioTrack,
  RemoteAuxiliaryTrack,
  RemoteTrackEvent,
  RemoteVideoTrack,
  RoomEvent,
  RoomOption,
  SPEAKER_DEFAULT,
  Scene,
  ScreenCaptureOption,
  TrackEvent,
  User,
  UserRole,
  UserRoleNumber,
  VideoDecoderDowngradeState,
  audioProfileMap,
  deviceDetector,
  getCameras,
  getMicrophones,
  getSpeakers,
  isDeviceGotFromWithoutPermission,
  loggerManager,
  screenProfileMap,
  setVersion,
  storage,
  videoProfileMap,
  VideoCodec
} from 'trtc-js-sdk-core';
import {
  capabilityCheck,
  checkSystemRequirementsInternal,
  IS_SEI_SUPPORTED
} from 'trtc-js-sdk-core/src/common/rtc-detection';
import * as eventManager from 'trtc-js-sdk-core/src/manager/event-manager';
import { limitCallFrequency } from 'trtc-js-sdk-core/src/utils/decorators/limit-call-frequency';
import { after, createDecorator } from 'trtc-js-sdk-core/src/utils/decorators/middleware';
import { introInfo } from 'trtc-js-sdk-core/src/utils/intro';
import {
  deepMerge,
  getViewListFromView,
  isArray,
  isBoolean,
  isEmpty,
  isNumber,
  isString,
  isUndefined
} from 'trtc-js-sdk-core/src/utils/utils';
import validateConfig, { isVideoProfileGreaterOrEqual } from './common/validate-config';
import { randStart, randStop, randUpdate } from './decorator/random-call';
import { TRTCEvent, TRTCEventSetToLogEmit, TRTCEventTypes } from './trtc-event';
import {
  EnterRoomConfig,
  LocalAudioConfig,
  LocalVideoConfig,
  RemoteAudioConfig,
  RemoteVideoConfig,
  ScreenShareConfig,
  StopRemoteAudioConfig,
  StopRemoteVideoConfig,
  TRTCDeviceAction,
  TRTCDeviceType,
  TRTCOptions,
  TRTCStreamType,
  TRTCType,
  UpdateLocalAudioConfig,
  UpdateScreenShareConfig,
  PluginStopOptionsMap,
  PluginUpdateOptionsMap,
  PluginStartOptionsMap,
  PluginWithAssets,
  TRTCStatistics,
  TRTCVideoType,
  VideoFrameConfig,
  FirstVideoFrameInfo,
  TRTCVolumeType,
  SwitchRoomConfig
} from './type';
import { convertStreamType, convertVideoProfile, getContentHintFromQosPreference } from './utils';
import { IPlugin, IPluginClass, createCore } from 'trtc-js-sdk-core/src/plugin';
import { catchEmitterError } from 'trtc-js-sdk-core/src/utils/catch-emitter';
import { debounce, clearDebounceCache } from 'trtc-js-sdk-core/src/utils/decorators/debounce';
import { version } from '../package.json';
import { ErrorCode, ErrorCodeDictionary } from './common/error-code';
import RtcError from './common/rtc-error';
import { validate, validateSync } from './common/validate';
import { addAPICallLog } from './decorator/add-api-call-log';
import { validatePlugin } from './decorator/validate-plugin';
import { AudioMixer } from './plugins/audio-mixer';
import { AUDIO_REFERENCE_TYPE, AudioProcessor } from './plugins/audio-processor';
import { AIDenoiser } from './plugins/ai-denoiser';
import innerEmitter from 'trtc-js-sdk-core/src/inner-emitter/inner-emitter';
import { INNER_EVENT } from 'trtc-js-sdk-core/src/inner-emitter/inner-type';
import { InnerEventTypes } from 'trtc-js-sdk-core/src/inner-emitter/inner-event';
import kvStatManager, { KV_REPORT_KEY_API, KV_REPORT_KEY_START_PLUGIN, KV_REPORT_KEY_STOP_PLUGIN, KV_REPORT_KEY_UPDATE_PLUGIN, KV_STAT_TYPE, outterKVStatManager } from 'trtc-js-sdk-core/src/manager/kv-stat-manager';
import pressureDetector from 'trtc-js-sdk-core/src/metrics/pressure-detector';
import { SEIPlugin } from './plugins/sei';
import { DebugPlugin } from './plugins/debug';
import { AudioDecoderPlugin } from './plugins/audio-decoder';
import { fromEvent, pipe, subscribe, switchMapTo, takeUntil, tap } from 'fastrx';
import { FSM } from 'afsm';
import { IS_ANDROID } from 'trtc-js-sdk-core/src/utils/environment';
import experimentalApi, { ExperimentalAPIFunctionMap } from 'trtc-js-sdk-core/src/experimental-api/experimental-api';
import { addCallQueue } from 'trtc-js-sdk-core/src/utils/decorators/api-call-queue';

let seq = 0;
const trtcInstanceSet: Set<TRTC> = new Set();
// 当前使用的 speaker
let lastSpeakerDeviceInfo: DeviceInfo | null = null;
setVersion(version);
storage.checkStorage();

const apiErrorModule = {
  RtcError,
  ErrorCode,
  ErrorCodeDictionary
};

/**
 * The TRTC object is created using {@link TRTC.create TRTC.create()} and provides core real-time audio and video capabilities:<br>
 * - Enter an audio and video room {@link TRTC#enterRoom enterRoom()}
 * - Exit the current audio and video room {@link TRTC#exitRoom exitRoom()}
 * - Preview/publish local video {@link TRTC#startLocalVideo startLocalVideo()}
 * - Capture/publish local audio {@link TRTC#startLocalAudio startLocalAudio()}
 * - Stop previewing/publishing local video {@link TRTC#stopLocalVideo stopLocalVideo()}
 * - Stop capturing/publishing local audio {@link TRTC#stopLocalAudio stopLocalAudio()}
 * - Watch remote video {@link TRTC#startRemoteVideo startRemoteVideo()}
 * - Stop watching remote video {@link TRTC#stopRemoteVideo stopRemoteVideo()}
 * - Mute/unmute remote audio {@link TRTC#muteRemoteAudio muteRemoteAudio()}
 *
 * The TRTC lifecycle is shown in the following figure:<br/>
 *
 * <img src="./assets/client-life-cycle.png" width="600"/>
 *
 * @class TRTC
 */
class TRTC extends EventEmitter<TRTCEventTypes> {
  protected _room: IRoom;
  private _eventListened = new Set<string>();
  private _localVideoTrack: LocalVideoTrack | null = null;
  private _localAudioTrack: LocalAudioTrack | null = null;
  private _localScreenTrack: LocalScreenTrack | null = null;
  private _localScreenAudioTrack: LocalScreenAudioTrack | null = null;
  private _localVideoConfig: LocalVideoConfig | null = null;
  private _localScreenConfig: ScreenShareConfig | null = null;
  private _localAudioConfig: LocalAudioConfig | null = null;
  private _remoteVideoConfigMap: Map<string, { config: RemoteVideoConfig, observer?: IntersectionObserver, visibleViewMap?: Map<Element, boolean>; }> = new Map();
  private _remoteAudioConfigMap: Map<string, RemoteAudioConfig> = new Map(); // 是否被订阅
  private _remoteAudioVolumeMap: Map<string, number> = new Map();
  private _remoteAudioMuteMap: Map<string, boolean> = new Map();
  private _mediaTrackMap: WeakMap<MediaStreamTrack, string> = new WeakMap();
  seq = ++seq;
  private _log = loggerManager.createLogger({ id: `t${this.seq}` });
  private static _loggerManager = loggerManager;
  private _plugins = new Map<string, IPlugin>();

  private _networkQuality: NetworkQuality | null = null;
  private _speakerId?: string;
  private enterRoomParams?: EnterRoomConfig;
  private _enableAutoSwitchWhenRecapturing = true;
  private _version = version;
  /**
   * Create a TRTC object for implementing functions such as entering a room, previewing, publishing, and subscribing streams.<br>
   *
   * **Note:**
   * - You must create a TRTC object first and call its methods and listen to its events to implement various functions required by the business.
   * @param {Array=} options.plugins List of registered plugins (optional).
   * @param {boolean=} [options.enableSEI=false] Whether to enable SEI sending and receiving function (optional). [Reference document](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#sendSEIMessage)
   * @param {string=} options.assetsPath The address of the static resource file that the plugin depends on (optional).
   * - Publish the node_modules/trtc-sdk-v5/assets directory to CDN or static resource server.
   * - Set the assetsPath parameter to the CDN address, for example: `TRTC.create({ assetsPath: 'https://xxx/assets' })`, the SDK will load the relevant resource files on demand.
   * @param {string=} options.userDefineRecordId is used to set the userDefineRecordId of cloud recording (optional).
   * - [Recommended value] The length is limited to 64 bytes, and only uppercase and lowercase English letters (a-zA-Z), numbers (0-9), underscores, and hyphens are allowed.
   * - [Reference document] [Cloud recording](https://trtc.io/document/45169?product=rtcengine&menulabel=serverfeaturesapis).
   * @example
   * // Create a TRTC object
   * const trtc = TRTC.create();
   *
   * @returns {TRTC} TRTC object
   */
  // @ts-ignore
  static create(options?: TRTCOptions): TRTC {

  }
  @validate(validateConfig.TRTC.create)
  private static _create(RoomClass: { new(params: RoomOption): IRoom; }, options?: TRTCOptions) {
    introInfo();
    const trtc = new TRTC(RoomClass, options || {});
    trtcInstanceSet.add(trtc);

    return trtc;
  }
  private constructor(RoomClass: { new(params: RoomOption): IRoom; }, options: TRTCOptions) {
    super();
    this._room = new RoomClass({
      logger: this._log,
      frameWorkType: TRTC.frameWorkType,
      ...options
    });
    this._room.videoDecodeFallbackType = options.videoDecodeFallback;
    if (isBoolean(options.enableAutoSwitchWhenRecapturing)) this._enableAutoSwitchWhenRecapturing = options.enableAutoSwitchWhenRecapturing;
    this._log.info(`create() ${JSON.stringify(options, (key, value) => {
      if (key === 'plugins') return value.map((item: IPluginClass) => item.Name);
      return value;
    })}`);
    console.warn((window as any).__TRTC_DEVTOOLS_DATA__);
    // @ts-ignore
    (window as any).__TRTC_DEVTOOLS_DATA__ = {
      ...(window as any).__TRTC_DEVTOOLS_DATA__,
      [`t${seq}`]: {
        instance: this,
        functions: [{
          name: 'create',
          startTimeStamp: Date.now(),
          endTimeStamp: Date.now(),
          params: options
        }]
      }
    };
    console.warn('end', (window as any).__TRTC_DEVTOOLS_DATA__);
    Object.defineProperties(this, {
      dumpAudio: {
        enumerable: false, value(dur: number) {
          return this._room.audioManager.dump(dur);
        }
      }
    });

    if (options.plugins) {
      options.plugins.forEach(Plugin => {
        this._use(Plugin, options.assetsPath);
      });
    }
    this._use(AudioMixer as any, options.assetsPath);
    this._use(AudioProcessor as any, options.assetsPath);
    this._use(AIDenoiser as any, options.assetsPath);
    this._use(AudioDecoderPlugin as any, options.assetsPath);
    this._use(DebugPlugin as any);
    if (options.enableSEI && IS_SEI_SUPPORTED) {
      this._use(SEIPlugin as any);
    }
    this._room.on(RoomEvent.AUDIO_VOLUME, (result: TRTCVolumeInfo[]) => {
      if (!result.find(item => item.userId === '') && this._localAudioTrack) {
        result.push({
          userId: '',
          volume: Math.floor((this._localAudioTrack.getInternalAudioLevelAfter3A() ?? this._localAudioTrack.getAudioLevel()) * 100),
          floatVolume: this._localAudioTrack.getInternalAudioLevelAfter3A() ?? this._localAudioTrack.getInternalAudioLevel()
        });
      }
      // volume 使用 db 作为单位，对齐声网算法。
      if (options.volumeType === TRTCVolumeType.DB) {
        result.forEach(item => {
          const track = (item.userId === '' ? this._localAudioTrack : this.room.remotePublishedUserMap.get(item.userId)?.remoteAudioTrack) as LocalAudioTrack | RemoteAudioTrack;
          if (track) {
            item.volume = track.dbVolume;
          }
        });
      }
      this.emit(TRTCEvent.AUDIO_VOLUME, { result: result.sort((a, b) => b.volume - a.volume) });
    });
    this._room.videoManager.on('error', (err: Error) => {
      this._log.error(new RtcError({
        code: ErrorCode.OPERATION_FAILED,
        extraCode: ErrorCodeDictionary.VIDEO_CONTEXT_ERROR,
        message: err.message,
        originError: err
      }));
    });

    this._listenEvents();
    this._initActiveSpeaker();
    // @ts-ignore
    // 捕获业务侧的事件回调代码报错，避免影响 SDK 内部逻辑
    catchEmitterError(this, 'trtc');
  }
  get room() {
    return this._room;
  }
  private _listenEvents() {
    // @ts-ignore
    eventManager.create(this, this._room)
      .add(RoomEvent.PEER_JOIN, (user: User) => {
        const { userId } = user;
        // 将map中的普通object转换成类对象
        this.emit(TRTCEvent.REMOTE_USER_ENTER, { userId });
      })
      .add(RoomEvent.PEER_LEAVE, userId => {
        this.emit(TRTCEvent.REMOTE_USER_EXIT, { userId });
        this._remoteAudioVolumeMap.delete(userId);
      })
      .add(RoomEvent.BANNED, (event: { reason: Exclude<BannedReason, 'user_time_out'>, message: string; }) => {
        // 被踢后清理进房相关状态，业务侧可以重新调用 enterRoom 接口进房
        this._exitRoom().finally(() => {
          this.emit(TRTCEvent.KICKED_OUT, { reason: event.reason });
        });
      })
      .add(RoomEvent.ERROR, (error: CoreError) => {
        // 清理进房相关状态，业务侧可以重新调用 enterRoom 接口进房
        this._exitRoom().finally(() => {
          this.emit(TRTCEvent.ERROR, RtcError.convertFrom(error));
        });
      })
      .add(RoomEvent.SIGNAL_CONNECTION_STATE_CHANGED, (event: { prevState: ConnectionState, state: ConnectionState; userId: string; }) => {
        this.emit(TRTCEvent.CONNECTION_STATE_CHANGED, event);
      })
      // .add(RoomEvent.MEDIA_CONNECTION_STATE_CHANGED, (event: { prevState: ConnectionState, state: ConnectionState; userId: string; }) => {
      //   this.emit(TRTCEvent.CONNECTION_STATE_CHANGED, { ...event, type: 'media' });
      // })
      .add(RoomEvent.NETWORK_QUALITY, (result: NetworkQuality) => {
        this._networkQuality = result;
        const limitedResult = {
          ...result,
          uplinkRTT: Math.min(result.uplinkRTT, MAX_RTT),
          downlinkRTT: Math.min(result.downlinkRTT, MAX_RTT)
        };
        this.emit(TRTCEvent.NETWORK_QUALITY, limitedResult);
      })
      .add(RoomEvent.REMOTE_PUBLISHED, (remotePublishedUser: PublishedUser) => {
        const remoteTracks = [remotePublishedUser.remoteAudioTrack, remotePublishedUser.remoteVideoTrack, remotePublishedUser.remoteAuxiliaryTrack];
        remoteTracks.forEach(remoteTrack => {
          eventManager.create(remoteTrack, remoteTrack)
            .add(TrackEvent.PLAYER_STATE_CHANGED, (event: any) => {
              const data = { ...event, userId: remotePublishedUser.userId };
              if (remoteTrack.kind === NAME.VIDEO) {
                data.streamType = convertStreamType(remoteTrack.streamType);
              }
              this.emit(remoteTrack.kind === NAME.AUDIO ? TRTCEvent.AUDIO_PLAY_STATE_CHANGED : TRTCEvent.VIDEO_PLAY_STATE_CHANGED, data);
            })
            .add(TrackEvent.ERROR, (error: CoreError) => {
              if (error.getCode() === CoreErrorCode.PLAY_NOT_ALLOWED) {
                this.emit(TRTCEvent.AUTOPLAY_FAILED, { userId: remoteTrack.userId, mediaType: remoteTrack.strMediaType, resume: () => remoteTrack.player.resume() });
              }
            });
        });
      })
      .add(RoomEvent.REMOTE_UNPUBLISHED, (remotePublishedUser: PublishedUser) => {
        const remoteTracks = [remotePublishedUser.remoteAudioTrack, remotePublishedUser.remoteVideoTrack, remotePublishedUser.remoteAuxiliaryTrack];
        remoteTracks.forEach(remoteTrack => {
          eventManager.remove(remoteTrack);
        });
      })
      .add(RoomEvent.REMOTE_PUBLISH_STATE_CHANGED, ({
        prevMuteState,
        muteState
      }: { prevMuteState: MuteState, muteState: MuteState; }) => {
        const { userId } = muteState;

        const prevAudioAvailable = prevMuteState.audioAvailable;
        const prevVideoAvailable = prevMuteState.videoAvailable;
        const { audioAvailable, videoAvailable } = muteState;

        if (!audioAvailable) {
          this._remoteAudioConfigMap.delete(userId);
        }
        if (!videoAvailable) {
          this._removeRemoteVideoConfig(userId, TRTCStreamType.Main);
        }
        if (!muteState.hasAuxiliary) {
          this._removeRemoteVideoConfig(userId, TRTCStreamType.Sub);
        }

        if (prevVideoAvailable !== videoAvailable) {
          // TODO: 远端推小流 + 订阅小流, SDK 先自动订阅再抛事件, 首帧视频回调可能抛出大流的分辨率
          if (videoAvailable) this._onVideoAvailable({ userId, streamType: TRTCStreamType.Main });
          else this._onVideoUnavailable({ userId, streamType: TRTCStreamType.Main });
          this.emit(videoAvailable ? TRTCEvent.REMOTE_VIDEO_AVAILABLE : TRTCEvent.REMOTE_VIDEO_UNAVAILABLE, { userId, streamType: TRTCStreamType.Main });
        }
        if (prevAudioAvailable !== audioAvailable) {
          if (audioAvailable) this._onAudioAvailable({ userId });
          else this._onAudioUnavailable({ userId, muteState });
          this.emit(audioAvailable ? TRTCEvent.REMOTE_AUDIO_AVAILABLE : TRTCEvent.REMOTE_AUDIO_UNAVAILABLE, { userId });
        }
        if (prevMuteState.hasAuxiliary !== muteState.hasAuxiliary) {
          if (muteState.hasAuxiliary) this._onVideoAvailable({ userId, streamType: TRTCStreamType.Sub });
          else this._onVideoUnavailable({ userId, streamType: TRTCStreamType.Sub });
          this.emit(muteState.hasAuxiliary ? TRTCEvent.REMOTE_VIDEO_AVAILABLE : TRTCEvent.REMOTE_VIDEO_UNAVAILABLE, { userId, streamType: TRTCStreamType.Sub });
        }
      })
      .add(RoomEvent.SEI_MESSAGE, event => {
        this.emit(TRTCEvent.SEI_MESSAGE, { ...event, streamType: convertStreamType(event.streamType) });
      })
      .add(RoomEvent.FIREWALL_RESTRICTION, () => {
        this.emit(TRTCEvent.ERROR, new RtcError({
          code: ErrorCode.OPERATION_FAILED,
          extraCode: ErrorCodeDictionary.FIREWALL_RESTRICTION
        }));
      })
      .add(RoomEvent.HEARTBEAT_REPORT, (report: HearBeatReport) => {
        const videoTypeMap = {
          2: TRTCVideoType.Big,
          3: TRTCVideoType.Small,
          7: TRTCVideoType.Sub
        };

        const stats: TRTCStatistics = {
          rtt: Math.min(report.msg_up_stream_info.msg_network_status.uint32_rtt || report.msg_down_stream_info[0]?.msg_network_status.uint32_rtt || this._networkQuality?.uplinkRTT || this._networkQuality?.downlinkRTT || 0, MAX_RTT),
          upLoss: this._networkQuality?.uplinkLoss || 0,
          downLoss: this._networkQuality?.downlinkLoss || 0,
          bytesSent: report.bytes_sent || 0,
          bytesReceived: report.bytes_received || 0,
          localStatistics: {
            audio: { bitrate: (report.msg_up_stream_info.msg_audio_status?.uint32_audio_codec_bitrate || 0) / 1000, audioLevel: (report.msg_up_stream_info.msg_audio_status?.uint32_audio_level || 0) / AUDIO_LEVEL_SCALE },
            video: report.msg_up_stream_info.msg_video_status.filter(item => videoTypeMap[item.uint32_video_stream_type as keyof typeof videoTypeMap]).map(item => ({
              bitrate: (item.uint32_video_codec_bitrate || 0) / 1000,
              width: item.uint32_video_width!,
              height: item.uint32_video_height!,
              frameRate: item.uint32_video_enc_fps!,
              videoType: videoTypeMap[item.uint32_video_stream_type as keyof typeof videoTypeMap]
            }))
          },
          remoteStatistics: report.msg_down_stream_info.map(item => ({
            userId: item.msg_user_info.str_identifier,
            audio: {
              bitrate: (item.msg_audio_status.uint32_audio_codec_bitrate || 0) / 1000,
              audioLevel: (item.msg_audio_status.uint32_audio_level || 0) / AUDIO_LEVEL_SCALE,
              point2pointDelay: (item.msg_audio_status.uint32_audio_p2p_delay || 0) + (item.msg_audio_status.uint32_audio_cache_ms || 0),
              jitterBufferDelay: item.msg_audio_status.uint32_audio_cache_ms || 0
            },
            video: item.msg_video_status.map(videoItem => ({
              bitrate: (videoItem.uint32_video_codec_bitrate || 0) / 1000,
              width: videoItem.uint32_video_width!,
              height: videoItem.uint32_video_height!,
              frameRate: videoItem.uint32_video_dec_fps!,
              videoType: videoTypeMap[videoItem.uint32_video_stream_type as keyof typeof videoTypeMap],
              point2pointDelay: (videoItem.uint32_video_p2p_delay || 0) + (videoItem.uint32_video_cache_ms || 0),
              jitterBufferDelay: videoItem.uint32_video_cache_ms || 0,
              codec: videoItem.uint32_video_codec
            }))
          }))
        };
        this.emit(TRTCEvent.STATISTICS, stats);
      })
      .add(RoomEvent.CUSTOM_MESSAGE, (data: CustomMessage) => {
        this.emit(TRTCEvent.CUSTOM_MESSAGE, data);
      })
      .add(RoomEvent.LAYER_DATA, event => this.emit(TRTCEvent.LAYER_DATA, event))
      .add(RoomEvent.FIRST_VIDEO_FRAME, (data: FirstVideoFrameInfo) => {
        this.emit(TRTCEvent.FIRST_VIDEO_FRAME, { ...data, streamType: convertStreamType(data.streamType) });
      })
      .add(RoomEvent.AUDIO_FRAME, (datas: { data: Float32Array | Float32Array[], userId: string, sampleRate: number, channelCount: number; }) => {
        this.emit(TRTCEvent.AUDIO_FRAME, datas);
      });
    eventManager.create(this, deviceDetector!)
      .add(DeviceChangeEvent.AUDIO_INPUT_ADDED, (device: DeviceInfo) => {
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Microphone, action: TRTCDeviceAction.Add, device });
      })
      .add(DeviceChangeEvent.AUDIO_INPUT_REMOVED, (device: DeviceInfo) => {
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Microphone, action: TRTCDeviceAction.Remove, device });
      })
      .add(DeviceChangeEvent.VIDEO_INPUT_ADDED, (device: DeviceInfo) => {
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Camera, action: TRTCDeviceAction.Add, device });
      })
      .add(DeviceChangeEvent.VIDEO_INPUT_REMOVED, (device: DeviceInfo) => {
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Camera, action: TRTCDeviceAction.Remove, device });
      })
      .add(DeviceChangeEvent.AUDIO_OUTPUT_ADDED, async (device: DeviceInfo) => {
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Add, device });
        // 当前使用的是 default 扬声器，插入新的扬声器变成了 default，则抛出 active 事件。
        if (lastSpeakerDeviceInfo && lastSpeakerDeviceInfo.deviceId === SPEAKER_DEFAULT) {
          const defaultSpeaker = (await getSpeakers()).find(speaker => speaker.deviceId === SPEAKER_DEFAULT);
          if (defaultSpeaker && lastSpeakerDeviceInfo.groupId !== defaultSpeaker.groupId) {
            lastSpeakerDeviceInfo = defaultSpeaker;
            this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: defaultSpeaker });
          }
        }
      })
      .add(DeviceChangeEvent.AUDIO_OUTPUT_REMOVED, async (device: DeviceInfo) => {
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Remove, device });
        // 当前使用的扬声器被移除，默认认为扬声器列表第一个是 active 扬声器
        const firstSpeaker = (await getSpeakers())[0];

        // 扬声器没变，不需要处理后续逻辑。
        if (!firstSpeaker || !lastSpeakerDeviceInfo || lastSpeakerDeviceInfo.groupId === firstSpeaker.groupId) return;

        const isSpeakerInUsedRemoved = lastSpeakerDeviceInfo.deviceId === device.deviceId;
        const isDefaultSpeakerInUsedAndDefaultChanged = lastSpeakerDeviceInfo.deviceId === SPEAKER_DEFAULT && lastSpeakerDeviceInfo.deviceId === firstSpeaker.deviceId;

        // 正在使用的扬声器被移除 或者 正在使用 default 扬声器，并且 default 变了。则将 firstSpeaker 视为 active。
        if ((isSpeakerInUsedRemoved || isDefaultSpeakerInUsedAndDefaultChanged)) {
          lastSpeakerDeviceInfo = firstSpeaker;
          this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: firstSpeaker });
        }
      });
  }


  /**
   * @private
   * 注册插件 <br>
   *
   * @example
   * import { VirtualBackground } from 'trtc-sdk-v5/plugins/video-effect/virtual-background';
   * trtc.use({ plugin: VirtualBackground });
   *
   * @example
   * import { VirtualBackground } from 'trtc-sdk-v5/plugins/video-effect/virtual-background';
   * trtc.use({ plugin: VirtualBackground, assetsPath: './js/assets/' });
   *
   * @example
   * // 简写使用
   * import { VirtualBackground } from 'trtc-sdk-v5/plugins/video-effect/virtual-background';
   * trtc.use(VirtualBackground);
   */
  @addAPICallLog({
    replaceArg: (pluginObject: PluginWithAssets | IPluginClass) => ({
      argIndex: 0,
      value: {
        name: 'plugin' in pluginObject ? pluginObject.plugin.Name : pluginObject.Name,
        assetsPath: 'assetsPath' in pluginObject ? pluginObject?.assetsPath : 'default'
      }
    })
  })
  use(pluginObject: PluginWithAssets | IPluginClass) {
    let plugin: IPluginClass;
    let assetsPath: string | undefined;

    if ('plugin' in pluginObject) {
      plugin = pluginObject.plugin;
      assetsPath = pluginObject.assetsPath;
    } else {
      plugin = pluginObject as IPluginClass;
    }

    this._use(plugin, assetsPath);
  }

  _use(pluginClass: IPluginClass, assetsPath?: string) {
    const existPlugin = this._plugins.get(pluginClass.Name);
    if (existPlugin) {
      this._log.warn('duplicate install plugin', pluginClass.Name);
      return;
    }
    // eslint-disable-next-line new-cap
    const pluginInstance = new pluginClass(createCore.call(this, { TRTC, room: this._room, errorModule: apiErrorModule, assetsPath }));
    this._plugins.set(pluginClass.Name, pluginInstance);

    if (pluginClass.autoStart) {
      this.startPlugin(pluginClass.Name as keyof PluginStartOptionsMap);
    }
  }

  /**
   * @typedef TurnServer
   * @property {string} url TURN server url
   * @property {string=} username TURN server auth user name
   * @property {string=} credential TURN server password
   * @property {string=} [credentialType=password] TURN server verify password type
   */

  /**
   * @typedef ProxyServer
   * @property {string} [websocketProxy] websocket service proxy
   * @property {string} [loggerProxy] log service agent
   * @property {TurnServer[]} [turnServer] media data transmission agent
   * @property {'all'|'relay'} [iceTransportPolicy='all'] 'all' gives priority to directly connecting to TRTC, and tries to go to the turn server if the connection fails.<br>
   * 'relay' forces the connection through the TURN server.
   */

  // 自动播放弹框配置
  // 自动订阅配置
  // 代理配置
  // 更新 this._log 的 userId 和 sdkappid
  /**
   * Enter a video call room.<br>
   * - Entering a room means starting a video call session. Only after entering the room successfully can you make audio and video calls with other users in the room.
   * - You can publish local audio and video streams through {@link TRTC#startLocalVideo startLocalVideo()} and {@link TRTC#startLocalAudio startLocalAudio()} respectively. After successful publishing, other users in the room will receive the {@link module:EVENT.REMOTE_AUDIO_AVAILABLE REMOTE_AUDIO_AVAILABLE} and {@link module:EVENT.REMOTE_VIDEO_AVAILABLE REMOTE_VIDEO_AVAILABLE} event notifications.
   * - By default, the SDK automatically plays remote audio. You need to call {@link TRTC#startRemoteVideo startRemoteVideo()} to play remote video.
   *
   * @param {object} options Enter room parameters
   * @param {number} options.sdkAppId sdkAppId <br>
   * You can obtain the sdkAppId information in the **Application Information** section after creating a new application by clicking **Application Management** > **Create Application** in the [TRTC Console](https://console.trtc.io/).
   * @param {string} options.userId User ID <br>
   * It is recommended to limit the length to 32 bytes, and only allow uppercase and lowercase English letters (a-zA-Z), numbers (0-9), underscores, and hyphens.
   * @param {string} options.userSig UserSig signature <br>
   * Please refer to [UserSig related](https://trtc.io/document/35166?platform=web&product=rtcengine&menulabel=coresdk) for the calculation method of userSig.
   * @param {number=} options.roomId
   * the value must be an integer between 1 and 4294967294<br>
   * <font color="red">If you need to use a string type room id, please use the strRoomId parameter. One of roomId and strRoomId must be passed in. If both are passed in, the roomId will be selected first.</font>
   * @param {string=} options.strRoomId
   * String type room id, the length is limited to 64 bytes, and only supports the following characters:
   * - Uppercase and lowercase English letters (a-zA-Z)
   * - Numbers (0-9)
   * - Space ! # $ % & ( ) + - : ; < = . > ? @ [ ] ^ _ { } | ~ ,
   * <font color="red">Note: It is recommended to use a numeric type roomId. The string type room id "123" is not the same room as the numeric type room id 123.</font>
   * @param {string} [options.scene] Application scene, currently supports the following two scenes:
   * - {@link module:TYPE.SCENE_RTC TRTC.TYPE.SCENE_RTC} (default) Real-time call scene, which is suitable for 1-to-1 audio and video calls, or online meetings with up to 300 participants. {@tutorial 04-info-uplink-limits}.
   * - {@link module:TYPE.SCENE_LIVE TRTC.TYPE.SCENE_LIVE} Interactive live streaming scene, which is suitable for online live streaming scenes with up to 100,000 people, but you need to specify the role field in the options parameter introduced next.
   * @param {string=} [options.role] User role, only meaningful in the {@link module:TYPE.SCENE_LIVE TRTC.TYPE.SCENE_LIVE} scene, and the {@link module:TYPE.SCENE_RTC TRTC.TYPE.SCENE_RTC} scene does not need to specify the role. Currently supports two roles:
   * - {@link module:TYPE.ROLE_ANCHOR TRTC.TYPE.ROLE_ANCHOR} (default) Anchor
   * - {@link module:TYPE.ROLE_AUDIENCE TRTC.TYPE.ROLE_AUDIENCE} Audience
   * Note: The audience role does not have the permission to publish local audio and video, only the permission to watch remote streams. If the audience wants to interact with the anchor by connecting to the microphone, please switch the role to the anchor through {@link TRTC#switchRole switchRole()} before publishing local audio and video.
   * @param {boolean} [options.autoReceiveAudio=true] Whether to automatically receive audio. When a remote user publishes audio, the SDK automatically plays the remote user's audio.
   * @param {boolean} [options.autoReceiveVideo=false] Whether to automatically receive video. When a remote user publishes video, the SDK automatically subscribes and decodes the remote video. You need to call {@link TRTC#startRemoteVideo startRemoteVideo} to play the remote video.
   * - The default value was changed to `false` since v5.6.0. Refer to [Breaking Changed for v5.6.0](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-00-info-update-guideline.html).
   * @param {boolean} [options.enableAutoPlayDialog] Whether to enable the SDK's automatic playback failure dialog box, default: true.
   * - Enabled by default. When automatic playback fails, the SDK will pop up a dialog box to guide the user to click the page to restore audio and video playback.
   * - Can be set to false in order to turn off. Refer to {@tutorial 21-advanced-auto-play-policy}.
   * @param {string|ProxyServer} [options.proxy] proxy config. Refer to {@tutorial 34-advanced-proxy}.
   * @param {string} [options.privateMapKey] Key for entering a room. If permission control is required, please carry this parameter (empty or incorrect value will cause a failure in entering the room).<br>[privateMapKey permission configuration](https://trtc.io/document/35157?product=rtcengine).
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * - {@link module:ERROR_CODE.ENV_NOT_SUPPORTED ENV_NOT_SUPPORTED}
   * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
   * @example
   * const trtc = TRTC.create();
   * await trtc.enterRoom({ roomId: 8888, sdkAppId, userId, userSig });
   */
  @validate(validateConfig.TRTC.enterRoom)
  @randStart('room', ([arg1]: [EnterRoomConfig], [arg2]: [EnterRoomConfig]) => (arg1.roomId || arg1.strRoomId) === (arg2.roomId || arg2.strRoomId) && arg1.userId === arg2.userId && arg1.sdkAppId === arg2.sdkAppId)
  @createDecorator<TRTC>(next => function (this: TRTC, params: EnterRoomConfig) {
    this._log.setUserId(params.userId);
    this._log.setSdkAppId(params.sdkAppId);
    return next.call(this, params);
  })
  @addAPICallLog()
  async enterRoom(params: EnterRoomConfig) {
    // TODO: dev 弹窗提示
    // if (!this._eventListened.has(TRTCEvent.ERROR)) {
    //   console.warn('You should listen to the error event to handle the error properly.');
    // }
    // if (!this._eventListened.has(TRTCEvent.AUTOPLAY_FAILED)) {
    //   console.warn('You should listen to the autoplay-failed event to recover play.');
    // }
    // if (!this._eventListened.has(TRTCEvent.REMOTE_VIDEO_AVAILABLE)) {
    //   console.warn('You should listen to the remote-video-available event to play remote video.');
    // }
    this.enterRoomParams = params;
    const { scene = Scene.RTC, enableAutoPlayDialog = true, autoReceiveAudio = true, autoReceiveVideo = false } = params;
    if (params.proxy) {
      this._room.setProxyServer(params.proxy);
      if (!isString(params.proxy) && params.proxy.turnServer) {
        this._room.setTurnServer?.(params.proxy.turnServer, params.proxy.iceTransportPolicy);
      }
    }
    this._room.enableAutoPlayDialog = enableAutoPlayDialog;
    this._room.autoReceiveAudio = autoReceiveAudio;
    this._room.autoReceiveVideo = autoReceiveVideo;

    // 隐藏参数
    // @ts-ignore
    if (isBoolean(params.preferHW)) this._room.preferHW = params.preferHW;
    if (params.playoutDelay) this._room.playoutDelay = params.playoutDelay; // https://tapd.woa.com/tapd_fe/10155901/story/detail/1010155901121620868
    if (params.jitterBufferDelay) this._room.jitterBufferDelay = params.jitterBufferDelay;

    // @ts-ignore
    const roomParams: TRTCParams = {
      sdkAppId: params.sdkAppId,
      userId: params.userId,
      userSig: params.userSig,
      privateMapKey: params.privateMapKey || null,
      latencyLevel: params.latencyLevel,
      role: params.role === UserRole.AUDIENCE ? UserRoleNumber.AUDIENCE : UserRoleNumber.ANCHOR,
      roomId: params.roomId || 0,
      strRoomId: params.strRoomId || '',
      businessInfo: params.businessInfo || null,
      streamId: null,
      userDefineRecordId: params.userDefineRecordId || null,

      // 内部参数不暴露
      // @ts-ignore
      frameWorkType: params.frameWorkType,
      // @ts-ignore
      component: params.component,
      // @ts-ignore
      language: params.language,
      priority: params.priority,

      // @ts-ignore
      useVp8: params.useVp8,
      // @ts-ignore
      useH265: params.useH265,
      // @ts-ignore
      keepAlive: params.keepAlive
    };
    if (params.strRoomId && !params.roomId) {
      this._room.useStringRoomId = true;
    } else {
      this._room.useStringRoomId = false;
    }
    await this._room.join(roomParams, scene, TRTC.frameWorkType);
    this._checkTrackToPublish();
    pressureDetector.start();
  }

  /**
   * Exit the current audio and video call room.
   * - After exiting the room, the connection with remote users will be closed, and remote audio and video will no longer be received and played, and the publishing of local audio and video will be stopped.
   * - The capture and preview of the local camera and microphone will not stop. You can call {@link TRTC#stopLocalVideo stopLocalVideo()} and {@link TRTC#stopLocalAudio stopLocalAudio()} to stop capturing local microphone and camera.
   * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * @memberof TRTC
   * @example
   * await trtc.exitRoom();
   */
  @addAPICallLog()
  async exitRoom() {
    return await this._exitRoom();
  }

  /**
   * Switch a video call room.<br>
   * - Switch the video call room that the audience is watching in live scene. It is faster than exit old room and then entering the new room, and can optimize the opening time in live broadcast and other scenarios.
   * - [Contact us](https://trtc.io/contact) to enable this API.
   *
   * @param {object} options Switch room parameters
   * @param {string} options.userSig UserSig signature <br>
   * Please refer to [UserSig related](https://trtc.io/document/35166) for the calculation method of userSig. <br/>
   * @param {number=} options.roomId
   * the value must be an integer between 1 and 4294967294<br>
   * <font color="red">Note: <br>1. The room to be switched and the current room must be of the same room type (both digital rooms or string rooms)<br> 2. Either roomId or strRoomId must be filled in. If both are filled in, roomId is preferred. </font>
   * @param {string=} options.strRoomId
   * String type room id, the length is limited to 64 bytes, and only supports the following characters:
   * - Uppercase and lowercase English letters (a-zA-Z)
   * - Numbers (0-9)
   * - Space ! # $ % & ( ) + - : ; < = . > ? @ [ ] ^ _ { } | ~ ,
   * @param {boolean=} [options.privateMapKey] Key for entering a room. If permission control is required, please carry this parameter (empty or incorrect value will cause a failure in entering the room).<br>[privateMapKey permission configuration](https://trtc.io/document/35157?product=rtcengine).
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
   * @example
   * const trtc = TRTC.create();
   * await trtc.enterRoom({
   *    ...
   *    roomId: 8888,
   *    scene: TRTC.TYPE.SCENE_LIVE,
   *    role: TRTC.TYPE.ROLE_AUDIENCE
   * });
   * await trtc.switchRoom({
   *    userSig,
   *    roomId: 9999
   * });
   */
  @validate(validateConfig.TRTC.switchRoom)
  @addAPICallLog()
  @addCallQueue()
  async switchRoom(options: SwitchRoomConfig) {
    if (this.room.isSwitchRoomSupported()) {
      await this._room.switchRoom(options);
    } else {
      await this.exitRoom();
      const enterRoomConfig = {
        ...this.enterRoomParams,
        ...options
      };
      await this.enterRoom(enterRoomConfig as EnterRoomConfig);
    }
  }

  /**
   * Switches the user role, only effective in TRTC.TYPE.SCENE_LIVE interactive live streaming mode.
   *
   * In interactive live streaming mode, a user may need to switch between "audience" and "anchor".
   * You can determine the role through the role field in {@link TRTC#enterRoom enterRoom()}, or switch roles after entering the room through switchRole.
   * - Audience switches to anchor, call trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR) to convert the user role to TRTC.TYPE.ROLE_ANCHOR anchor role, and then call {@link TRTC#startLocalVideo startLocalVideo()} and {@link TRTC#startLocalAudio startLocalAudio()} to publish local audio and video as needed.
   * - Anchor switches to audience, call trtc.switchRole(TRTC.TYPE.ROLE_AUDIENCE) to convert the user role to TRTC.TYPE.ROLE_AUDIENCE audience role. If there is already published local audio and video, the SDK will cancel the publishing of local audio and video.
   * > !
   * > - This interface can only be called after entering the room successfully.
   * > - After closing the camera and microphone, it is recommended to switch to the audience role in time to avoid the anchor role occupying the resources of 50 upstreams.
   * @param {string} role User role
   * - TRTC.TYPE.ROLE_ANCHOR anchor, can publish local audio and video, up to 50 anchors can publish local audio and video in a single room at the same time.
   * - TRTC.TYPE.ROLE_AUDIENCE audience, cannot publish local audio and video, can only watch remote streams, and there is no upper limit on the number of audience members in a single room.
   * @param {object} [option]
   * @param {string} [option.privateMapKey] `Since v5.3.0+` <br>
   * The privateMapKey may expire after a timeout, so you can use this parameter to update the privateMapKey.
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.INVALID_OPERATION INVALID_OPERATION}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
   * @memberof TRTC
   * @example
   * // After entering the room successfully
   * // TRTC.TYPE.SCENE_LIVE interactive live streaming mode, audience switches to anchor
   * await trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR);
   * // Switch from audience role to anchor role and start streaming
   * await trtc.startLocalVideo();
   *
   * // TRTC.TYPE.SCENE_LIVE interactive live streaming mode, anchor switches to audience
   * await trtc.switchRole(TRTC.TYPE.ROLE_AUDIENCE);
   * @example
   * // Since v5.3.0+
   * await trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR, { privateMapKey: 'your new privateMapKey' });
   */
  @validate(validateConfig.TRTC.switchRole)
  @randUpdate('room', { merge: (arg1, arg2: any) => arg2 })
  @addAPICallLog()
  async switchRole(role: UserRole, option?: { privateMapKey?: string; latencyLevel?: number; }) {
    if (option?.privateMapKey) {
      this._room.privateMapKey = option.privateMapKey;
    }
    if (option?.latencyLevel) {
      this._room.latencyLevel = option.latencyLevel;
    }
    await this._room.switchRole(role);
    if (role === UserRole.ANCHOR) {
      this._checkTrackToPublish();
    }
  }

  /**
   * Destroy the TRTC instance <br/>
   *
   * After exiting the room, if the business side no longer needs to use trtc, you need to call this interface to destroy the trtc instance in time and release related resources.
   *
   * Note:
   *  - The trtc instance after destruction cannot be used again.
   *  - If you have entered the room, you need to call the {@link TRTC#exitRoom TRTC.exitRoom} interface to exit the room successfully before calling this interface to destroy trtc.
   *
   * @example
   * // When the call is over
   * await trtc.exitRoom();
   * // If the trtc is no longer needed, destroy the trtc and release the reference.
   * trtc.destroy();
   * trtc = null;
   * @throws {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * @memberof TRTC
   */
  @addAPICallLog()
  destroy() {
    // API 层事件销毁
    eventManager.remove(this);
    this.removeAllListeners();
    this._room.destroy();
    trtcInstanceSet.delete(this);
    if (trtcInstanceSet.size === 0) {
      pressureDetector.destroy();
    }
    if (this._localAudioTrack) {
      this.stopLocalAudio();
    }
    if (this._localVideoTrack) {
      this.stopLocalVideo();
    }
    if (this._localScreenTrack) {
      this.stopScreenShare();
    }
    // 销毁插件
    this._plugins.forEach(plugin => plugin.destroy?.());
    this._plugins.clear();
    innerEmitter.off(INNER_EVENT.LOCAL_TRACK_CAPTURE_SUCCESS, this._onLocalTrackCaptured, this);
  }

  /**
   * Start collecting audio from the local microphone and publish it to the current room.
   * - When to call: can be called before or after entering the room, cannot be called repeatedly.
   * - Only one microphone can be opened for a trtc instance. If you need to open another microphone for testing in the case of already opening one microphone, you can create multiple trtc instances to achieve it.
   *
   * @param {object} [config] - Configuration item
   * @param {boolean} [config.publish] - Whether to publish local audio to the room, default is true. If you call this interface before entering the room and publish = true, the SDK will automatically publish after entering the room. You can get the publish state by listening this event {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}.
   * @param {boolean} [config.mute] - Whether to mute microphone. Refer to: {@tutorial 15-basic-dynamic-add-video}.
   * @param {object} [config.option] - Local audio options
   * @param {string} [config.option.microphoneId]- Specify which microphone to use
   * @param {MediaStreamTrack} [config.option.audioTrack] - Custom audioTrack. {@tutorial 20-advanced-customized-capture-rendering}.
   * @param {number} [config.option.captureVolume] - Set the capture volume of microphone. The default value is 100. Setting above 100 enlarges the capture volume. Since v5.2.1+.
   * @param {number} [config.option.earMonitorVolume] - Set the ear return volume, value range [0, 100], the local microphone is muted by default.
   * @param {string} [config.option.profile] - Audio encoding configuration, default {@link module:TYPE.AUDIO_PROFILE_STANDARD TRTC.TYPE.AUDIO_PROFILE_STANDARD}
   * @throws
   * - {@link module:ERROR_CODE.ENV_NOT_SUPPORTED ENV_NOT_SUPPORTED}
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
   * @example
   * // Collect the default microphone and publish
   * await trtc.startLocalAudio();
   * @example
   * // The following is a code example for testing microphone volume, which can be used for microphone volume detection.
   * trtc.enableAudioVolumeEvaluation();
   * trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => { });
   * // No need to publish audio for testing microphone
   * await trtc.startLocalAudio({ publish: false });
   * // After the test is completed, turn off the microphone
   * await trtc.stopLocalAudio();
   * @memberof TRTC
   */
  @validate(validateConfig.TRTC.startLocalAudio)
  @randStart('audio', ([arg1]: [LocalAudioConfig], [arg2]: [LocalAudioConfig]) => arg1?.option?.microphoneId === arg2?.option?.microphoneId)
  @addAPICallLog()
  async startLocalAudio(config: LocalAudioConfig = { publish: true }) {
    if (this._localAudioTrack) {
      this._log.warn('local audio is already started');
      return;
    }
    const { publish = true, mute, option } = config;
    const localAudioTrack = new LocalAudioTrack(this._room.audioManager);
    const captureOption: CaptureOption = {};
    const playOption: PlayOption = { muted: true };
    // TODO: 增加 capture 参数
    if (option) {
      if (!isUndefined(option.microphoneId)) {
        captureOption.deviceId = option.microphoneId;
      } else if (!isUndefined(option.audioTrack)) {
        captureOption.customSource = option.audioTrack;
      }
      if (option && isNumber(option.captureVolume)) {
        localAudioTrack.setCaptureVolume(option.captureVolume);
      }

      if (!isUndefined(option.profile)) {
        if (isString(option.profile)) {
          audioProfileMap[option.profile] && localAudioTrack.setProfile(audioProfileMap[option.profile]);
        } else {
          localAudioTrack.setProfile(option.profile);
        }
      }
      if (isNumber(option.earMonitorVolume)) {
        playOption.muted = !(option.earMonitorVolume > 0);
        playOption.volume = option.earMonitorVolume;
      }
      // 3A 配置
      if (!isUndefined(option.echoCancellation)) {
        localAudioTrack.profile.echoCancellation = option.echoCancellation;
      }
      if (!isUndefined(option.noiseSuppression)) {
        localAudioTrack.profile.noiseSuppression = option.noiseSuppression;
      }
      if (!isUndefined(option.autoGainControl)) {
        localAudioTrack.profile.autoGainControl = option.autoGainControl;
      }
      if (isBoolean(this._enableAutoSwitchWhenRecapturing)) {
        localAudioTrack.enableAutoSwitchWhenRecapturing = this._enableAutoSwitchWhenRecapturing;
      }
    }
    localAudioTrack.on(LocalTrackEvent.DEVICE_RECOVER_FAILED, (error: any) => {
      this.emit(TRTCEvent.ERROR, new RtcError({
        code: ErrorCode.DEVICE_ERROR,
        extraCode: ErrorCodeDictionary.MICROPHONE_RECOVER_FAILED,
        messageParams: { error }
      }));
    });
    localAudioTrack.on(LocalTrackEvent.DEVICE_CHANGED, (device: DeviceInfo): void => {
      this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Microphone, action: TRTCDeviceAction.Active, device });
    });
    localAudioTrack.on(LocalTrackEvent.PUBLISH_STATE_CHANGED, (event: LocalTrackEventTypes[LocalTrackEvent.PUBLISH_STATE_CHANGED][0]) => {
      let error;
      if (event.error) {
        error = RtcError.convertFrom(event.error);
      }
      this.emit(TRTCEvent.PUBLISH_STATE_CHANGED, { ...event, error });
    });
    localAudioTrack.on(LocalTrackEvent.ENCODE_FAILED, () => {
      this.emit(TRTCEvent.ERROR, new RtcError({
        code: ErrorCode.OPERATION_FAILED,
        extraCode: ErrorCodeDictionary.AUDIO_ENCODE_FAILED,
        message: 'audio encode failed'
      }));
    });
    this._listenOutputTrackChanged(localAudioTrack);
    if (this._speakerId) localAudioTrack.setAudioOutput(this._speakerId);
    await localAudioTrack.capture(captureOption);

    if (!isUndefined(mute)) {
      localAudioTrack.setMute(mute);
    }

    eventManager.create(localAudioTrack, localAudioTrack)
      .add(TrackEvent.PLAYER_STATE_CHANGED, (event: any) => {
        this.emit(TRTCEvent.AUDIO_PLAY_STATE_CHANGED, { ...event, userId: '' });
      });

    if (publish && this._room.isJoined) {
      this._room.publish(localAudioTrack).catch(() => { });
    }
    this._localAudioTrack = localAudioTrack;
    this._localAudioConfig = { ...config, publish };

    await this._updateAudioPlayOption({ playOption, track: localAudioTrack });
    // 开启麦克风 pcm 数据回调
    innerEmitter.emit(INNER_EVENT.LOCAL_AUDIO_STARTED, { userId: '', room: this.room });
  }

  /**
   * Update the configuration of the local microphone.
   * - When to call: This interface needs to be called after {@link TRTC#startLocalAudio startLocalAudio()} is successful and can be called multiple times.
   * - This method uses incremental update: only update the passed parameters, and keep the parameters that are not passed unchanged.
   * @param {object} [config]
   * @param {boolean} [config.publish] - Whether to publish local audio to the room. You can get the publish state by listening this event {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}.
   * @param {boolean} [config.mute] - Whether to mute microphone. Refer to: {@tutorial 15-basic-dynamic-add-video}.
   * @param {object} [config.option] - Local audio configuration
   * @param {string} [config.option.microphoneId] - Specify which microphone to use to switch microphones.
   * @param {MediaStreamTrack} [config.option.audioTrack] - Custom audioTrack. {@tutorial 20-advanced-customized-capture-rendering}.
   * @param {number} [config.option.captureVolume] - Set the capture volume of microphone. The default value is 100. Setting above 100 enlarges the capture volume. Since v5.2.1+.
   * @param {number} [config.option.earMonitorVolume] - Set the ear return volume, value range [0, 100], the local microphone is muted by default.
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * @example
   * // Switch microphone
   * const microphoneList = await TRTC.getMicrophoneList();
   * if (microphoneList[1]) {
   *   await trtc.updateLocalAudio({ option: { microphoneId: microphoneList[1].deviceId }});
   * }
   * @memberof TRTC
   */
  @validate(validateConfig.TRTC.updateLocalAudio)
  @randUpdate('audio', { debounce: { delay: 200, getKey: () => `${seq}-localAudio`, isNeedToDebounce: (config: UpdateLocalAudioConfig) => !isUndefined(config.option?.captureVolume) } })
  @addAPICallLog()
  async updateLocalAudio(config: UpdateLocalAudioConfig) {
    // replace track
    // 设置采集音量
    // 改变推流状态
    if (!this._localAudioTrack || !this._localAudioConfig) return;
    const { publish, mute, option } = config;
    const playOption: PlayOption = {};
    if (option) {
      if (option.microphoneId) {
        await this._localAudioTrack.switchDevice(option.microphoneId);
      } else if (!isUndefined(option.audioTrack)) {
        await this._localAudioTrack.setInputMediaStreamTrack(option.audioTrack as MediaStreamAudioTrack);
      }
      if (!isUndefined(option.captureVolume)) {
        this._localAudioTrack.setCaptureVolume(option.captureVolume);
      }
      if (!isUndefined(option.earMonitorVolume)) {
        playOption.muted = !(option.earMonitorVolume > 0);
        playOption.volume = option.earMonitorVolume;
      }
      await this._localAudioTrack.update3A(option);
    }

    if (this._room.isJoined && !isUndefined(publish)) {
      if (publish && !this._localAudioConfig.publish) {
        this._room.publish(this._localAudioTrack).catch(() => { });
      }
      if (this._localAudioConfig.publish && !publish) {
        this._room.unpublish(this._localAudioTrack).catch(() => { });
      }
    }

    if (!isUndefined(mute)) {
      this._localAudioTrack.setMute(mute);
    }

    await this._updateAudioPlayOption({ playOption, track: this._localAudioTrack, prevConfig: this._localAudioConfig });

    deepMerge(this._localAudioConfig, config);
  }

  /**
   * Stop collecting and publishing the local microphone.
   * - If you just want to mute the microphone, please use updateLocalAudio({ mute: true }). Refer to: {@tutorial 15-basic-dynamic-add-video}.
   * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * @example
   * await trtc.stopLocalAudio();
   */
  @randStop('audio')
  @addAPICallLog()
  async stopLocalAudio() {
    if (!this._localAudioTrack) return;
    if (this._room.isJoined) {
      // unpublish 不会失败，catch error
      await this._room.unpublish(this._localAudioTrack).catch(() => { });
    }

    // 停止麦克风 pcm 数据回调
    innerEmitter.emit(INNER_EVENT.LOCAL_AUDIO_STOPPED, { userId: '', room: this.room });

    // TODO: 停止插件
    this._localAudioTrack.stop();
    this._localAudioTrack.close();
    this._room.audioManager.removeInput(this._localAudioTrack);
    eventManager.remove(this._localAudioTrack);
    this._localAudioTrack = null;
    this._localAudioConfig = null;
  }

  /**
   * @typedef {object|string} VideoProfile - Configuration for local video stream
   *
   * Video configuration parameters, can use preset values in string format or custom resolution and other parameters
   * | Video Profile | Resolution (Width x Height) | Frame Rate (fps) | Bitrate (kbps) | Note |
   * | :---       | :---           | :---      | :---      | :--- |
   * | 120p_2       | 160 x 120      | 15        | 100        | v5.1.1+ |
   * | 180p       | 320 x 180      | 15        | 350       ||
   * | 180p_2       | 320 x 180      | 15        | 150       | v5.1.1+ |
   * | 240p       | 320 x 240      | 15        | 400       ||
   * | 240p_2       | 320 x 240      | 15        | 200       | v5.1.1+ |
   * | 360p       | 640 x 360      | 15        | 800       ||
   * | 360p_2       | 640 x 360      | 15        | 400       | v5.1.1+ |
   * | 480p       | 640 x 480      | 15        | 900       ||
   * | 480p_2       | 640 x 480      | 15        | 500       | Default value, v5.1.1+ |
   * | 720p       | 1280 x 720     | 15        | 1500      ||
   * | 1080p      | 1920 x 1080    | 15        | 2000      ||
   * | 1440p      | 2560 x 1440    | 30        | 4860      ||
   * | 4K         | 3840 x 2160    | 30        | 9000      ||
    * @property {number} width - Video width
    * @property {number} height - Video height
    * @property {number} frameRate - Video frame rate
    * @property {number} bitrate - Video bitrate
   * @example
   * const config = {
   *  option: {
   *   profile: '480p',
   *  },
   * }
   * await trtc.startLocalVideo(config);
   * @example
   * const config = {
   *  option: {
   *    profile: {
   *      width: 640,
   *      height: 480,
   *      frameRate: 15,
   *      bitrate: 900,
   *    }
   *  }
   * }
   * await trtc.startLocalVideo(config);
   */

  /**
   * Start collecting video from the local camera, play the camera's video on the specified HTMLElement tag, and publish the camera's video to the current room.
   * - When to call: can be called before or after entering the room, but cannot be called repeatedly.
   * - Only one camera can be started per TRTC instance. If you need to start another camera for testing while one camera is already started, you can create multiple TRTC instances to achieve this.

   * @param {object} [config]
   * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - The HTMLElement instance or ID for local video preview. If not passed or passed as null, the video will not be played.
   * @param {boolean} [config.publish] - Whether to publish the local video to the room. If you call this interface before entering the room and publish = true, the SDK will automatically publish after entering the room. You can get the publish state by listening to this event {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}.
   * @param {boolean | string} [config.mute] - Whether to mute the camera. Supports passing in an image URL string, the image will be published instead of the original camera stream. Other users in the room will receive the REMOTE_AUDIO_AVAILABLE event. It does not support calling when the camera is turned off. More information: {@tutorial 15-basic-dynamic-add-video}.
   * @param {object} [config.option] - Local video configuration
   * @param {string} [config.option.cameraId] - Specify which camera to use for switching cameras.
   * @param {boolean} [config.option.useFrontCamera] - Whether to use the front camera.
   * @param {MediaStreamTrack} [config.option.videoTrack] - Custom videoTrack. {@tutorial 20-advanced-customized-capture-rendering}.
   * @param {'view' | 'publish' | 'both' | boolean} [config.option.mirror] - Video mirroring mode, default is 'view'.
   * - 'view': You see yourself as a mirror image, and the other person sees you as a non-mirror image.
   * - 'publish': The other person sees you as a mirror image, and you see yourself as a non-mirror image.
   * - 'both': You see yourself as a mirror image, and the other person sees you as a mirror image.
   * - false: Boolean value, represents no mirroring.
   *
   * <font color="orange"> Note: Before version 5.3.2, only boolean can be passed, where true represents local preview mirroring, and false represents no mirroring.</font>
   * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - Video fill mode. The default is `cover`. Refer to the {@link https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit CSS object-fit} property.
   * @param {string | VideoProfile} [config.option.profile] - Video encoding parameters for the main video. Default value is `480p_2`.
   * @param {string | boolean | VideoProfile} [config.option.small] - Video encoding parameters for the small video. Refer to {@tutorial 27-advanced-small-stream}
   * @param {QOS_PREFERENCE_SMOOTH|QOS_PREFERENCE_CLEAR} [config.option.qosPreference] - Set the video encoding strategy for weak networks. Smooth first (default) ({@link module:TYPE.QOS_PREFERENCE_SMOOTH QOS_PREFERENCE_SMOOTH}) or Clear first ({@link module:TYPE.QOS_PREFERENCE_CLEAR QOS_PREFERENCE_CLEAR}).
   * @param {boolean} [config.option.avoidCropping] - On PC Chrome and Firefox, capturing low-resolution 16/9 videos like 360p or 180p may result in cropping issues. Setting this parameter to true can avoid cropping.
   * @param {0 | 90 | 180 | 270} [config.option.rotation] - Set the clockwise rotation angle of the local camera. Since `v5.11.0`.
   * @throws
   * - {@link module:ERROR_CODE.ENV_NOT_SUPPORTED ENV_NOT_SUPPORTED}
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
   * @example
   * // Preview and publish the camera
   * await trtc.startLocalVideo({
   *   view: document.getElementById('localVideo'), // Preview the video on the element with the DOM elementId of localVideo.
   * });
   * @example
   * // Preview the camera without publishing. Can be used for camera testing.
   * const config = {
   *   view: document.getElementById('localVideo'), // Preview the video on the element with the DOM elementId of localVideo.
   *   publish: false // Do not publish the camera
   * }
   * await trtc.startLocalVideo(config);
   * // Call updateLocalVideo when you need to publish the video
   * await trtc.updateLocalVideo({ publish: true });
   * @example
   * // Use a specified camera.
   * const cameraList = await TRTC.getCameraList();
   * if (cameraList[0]) {
   *   await trtc.startLocalVideo({
   *     view: document.getElementById('localVideo'), // Preview the video on the element with the DOM elementId of localVideo.
   *     option: {
   *       cameraId: cameraList[0].deviceId,
   *     }
   *   });
   * }
   *
   * // Use the front camera on a mobile device.
   * await trtc.startLocalVideo({ view, option: { useFrontCamera: true }});
   * // Use the rear camera on a mobile device.
   * await trtc.startLocalVideo({ view, option: { useFrontCamera: false }});
   * @memberof TRTC
   */
  @validate(validateConfig.TRTC.startLocalVideo)
  @randStart('video', ([arg1]: [LocalVideoConfig], [arg2]: [LocalVideoConfig]) => arg1?.option?.cameraId === arg2?.option?.cameraId)
  @addAPICallLog()
  async startLocalVideo(config: LocalVideoConfig = { publish: true, view: null, capture: true }) {
    if (this._localVideoTrack) {
      this._log.warn('local video is already started');
      return;
    }
    const { view, publish = true, capture = true, mute, option } = config;
    const localVideoTrack = new LocalVideoTrack(this._room.videoManager);
    const captureOption: CaptureOption = {};
    const playOption: PlayOption = {};
    if (option) {
      if (isBoolean(option.avoidCropping)) localVideoTrack.avoidCropping = option.avoidCropping;
      if (option.cameraId) {
        captureOption.deviceId = option.cameraId;
      } else if (!isUndefined(option.useFrontCamera)) {
        captureOption.facingMode = option.useFrontCamera ? NAME.FACING_MODE_USER : NAME.FACING_MODE_ENVIRONMENT;
      } else if (!isUndefined(option.videoTrack)) {
        captureOption.customSource = option.videoTrack;
      }
      if (!isUndefined(option.retryWhenExactFailed)) {
        captureOption.retryWhenExactFailed = option.retryWhenExactFailed;
      }

      if (option.qosPreference) {
        captureOption.contentHint = getContentHintFromQosPreference(option.qosPreference);
      }
      if (!isUndefined(option.profile)) {
        if (isString(option.profile)) {
          videoProfileMap[option.profile] && localVideoTrack.setProfile(videoProfileMap[option.profile]);
        } else {
          localVideoTrack.setProfile(option.profile);
        }
      }
      if (!isUndefined(option.fillMode)) {
        playOption.objectFit = option.fillMode;
      }
      if (!isUndefined(option.mirror)) {
        playOption.mirror = option.mirror;
      }
      if (!isUndefined(option.small)) {
        if (!isUndefined(option.smallMode)) {
          this._room.smallMode = option.smallMode;
        }
        if (isBoolean(option.small) && option.small === false) {
          localVideoTrack.stopSmall();
        } else {
          localVideoTrack.updateSmallConfig(convertVideoProfile(option.small, true));
        }
      }
      if (!isUndefined(option.rotation)) {
        localVideoTrack.setRotation(option.rotation);
      }
      if (isBoolean(this._enableAutoSwitchWhenRecapturing)) {
        localVideoTrack.enableAutoSwitchWhenRecapturing = this._enableAutoSwitchWhenRecapturing;
      }
    }
    localVideoTrack.once(TrackEvent.FIRST_VIDEO_FRAME, (data: FirstVideoFrameInfo) => {
      this.emit(TRTCEvent.FIRST_VIDEO_FRAME, {
        ...data,
        streamType: convertStreamType(data.streamType)
      });
    });
    localVideoTrack.on(LocalTrackEvent.DEVICE_RECOVER_FAILED, (error: any) => {
      this.emit(TRTCEvent.ERROR, new RtcError({
        code: ErrorCode.DEVICE_ERROR,
        extraCode: ErrorCodeDictionary.CAMERA_RECOVER_FAILED,
        messageParams: { error }
      }));
    });
    localVideoTrack.on(LocalTrackEvent.DEVICE_CHANGED, (device: DeviceInfo) => {
      this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Camera, action: TRTCDeviceAction.Active, device });
    });
    localVideoTrack.on(LocalTrackEvent.PUBLISH_STATE_CHANGED, (event: LocalTrackEventTypes[LocalTrackEvent.PUBLISH_STATE_CHANGED][0]) => {
      let error;
      if (event.error) {
        error = RtcError.convertFrom(event.error);
      }
      this.emit(TRTCEvent.PUBLISH_STATE_CHANGED, { ...event, error });
    });
    localVideoTrack.on(LocalTrackEvent.ENCODE_FAILED, () => {
      this.emit(TRTCEvent.ERROR, new RtcError({
        code: ErrorCode.OPERATION_FAILED,
        extraCode: ErrorCodeDictionary.VIDEO_ENCODE_FAILED,
        message: 'video encode failed'
      }));
    });
    this._listenOutputTrackChanged(localVideoTrack);
    // 采集，自定义源
    if (capture) await localVideoTrack.capture(captureOption);
    else {
      localVideoTrack.manager?.changeInput(localVideoTrack); // 即使未采集，vm 也需要其作为输入
    }
    if (!isUndefined(mute)) {
      await localVideoTrack.setMute(mute);
    }

    eventManager.create(localVideoTrack, localVideoTrack)
      .add(TrackEvent.PLAYER_STATE_CHANGED, (event: any) => {
        this.emit(TRTCEvent.VIDEO_PLAY_STATE_CHANGED, { ...event, userId: '', streamType: TRTCStreamType.Main });
      });
    // 是否推流
    if (publish && this._room.isJoined) {
      this._room.publish(localVideoTrack).catch(() => { });
    }

    this._localVideoTrack = localVideoTrack;
    this._localVideoConfig = { ...config, view, publish, capture };

    await this._updateVideoPlayOption({ view, playOption, track: localVideoTrack });
  }

  /**
   * Update the local camera configuration.
   * - This interface needs to be called after {@link TRTC#startLocalVideo startLocalVideo()} is successful.
   * - This interface can be called multiple times.
   * - This method uses incremental update: only updates the passed-in parameters, and keeps the parameters that are not passed in unchanged.
   * @param {object} [config]
   * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - The HTMLElement instance or Id of the preview camera. If not passed in or passed in null, the video will not be rendered, but still pushes the stream and consumes bandwidth.
   * @param {boolean} [config.publish] - Whether to publish the local video to the room. You can get the publish state by listening this event {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}.
   * @param {boolean | string} [config.mute] - Whether to mute the camera. Supports passing in image url string, the image will be published instead of origin camera stream, Other users in the room will receive the REMOTE_AUDIO_AVAILABLE event. It does not support calling when the camera is turned off. More information: {@tutorial 15-basic-dynamic-add-video}.
   * @param {object} [config.option] - Local video configuration
   * @param {string} [config.option.cameraId] - Specify which camera to use
   * @param {boolean} [config.option.useFrontCamera] - Whether to use the front camera
   * @param {MediaStreamTrack} [config.option.videoTrack] - Custom videoTrack. {@tutorial 20-advanced-customized-capture-rendering}.
   * @param {'view' | 'publish' | 'both' | boolean} [config.option.mirror] - Video mirroring mode, default is 'view'.
   * - 'view': You see yourself as a mirror image, and the other person sees you as a non-mirror image.
   * - 'publish': The other person sees you as a mirror image, and you see yourself as a non-mirror image.
   * - 'both': You see yourself as a mirror image, and the other person sees you as a mirror image.
   * - false: Boolean value, represents no mirroring.
   * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - Video fill mode. Refer to the {@link https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit| CSS object-fit} property
   * @param {string | VideoProfile} [config.option.profile] - Video encoding parameters for the main stream
   * @param {string | boolean | VideoProfile} [config.option.small] - Video encoding parameters for the small video. Refer to {@tutorial 27-advanced-small-stream}
   * @param {QOS_PREFERENCE_SMOOTH|QOS_PREFERENCE_CLEAR} [config.option.qosPreference] - Set the video encoding strategy for weak networks. Smooth first ({@link module:TYPE.QOS_PREFERENCE_SMOOTH QOS_PREFERENCE_SMOOTH}) or Clear first ({@link module:TYPE.QOS_PREFERENCE_CLEAR QOS_ PREFERENCE_SMOOTH})
   * @param {0 | 90 | 180 | 270} [config.option.rotation] - Set the clockwise rotation angle of the local camera. Since `v5.11.0`.
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * @example
   * // Switch camera
   * const cameraList = await TRTC.getCameraList();
   * if (cameraList[1]) {
   *   await trtc.updateLocalVideo({ option: { cameraId: cameraList[1].deviceId }});
   * }
   * @example
   * // Stop publishing video, but keep local preview
   * await trtc.updateLocalVideo({ publish:false });
   * @memberof TRTC
   */
  @validate(validateConfig.TRTC.updateLocalVideo)
  @randUpdate('video')
  @addAPICallLog()
  async updateLocalVideo(config: LocalVideoConfig) {
    if (!this._localVideoTrack || !this._localVideoConfig) return;
    const { view, publish, mute, capture, option } = config;
    const playOption: PlayOption = {};
    if (this._localVideoConfig.capture) {
      // 之前采集了，现在也采集，则增量更新
      if (capture !== false) {
        if (option?.cameraId) {
          await this._localVideoTrack.switchDevice(option?.cameraId);
        } else if (!isUndefined(option?.useFrontCamera)) {
          await this._localVideoTrack.switchDevice(option?.useFrontCamera ? NAME.FACING_MODE_USER : NAME.FACING_MODE_ENVIRONMENT);
        } else if (!isUndefined(option?.videoTrack)) {
          await this._localVideoTrack.setInputMediaStreamTrack(option?.videoTrack as MediaStreamVideoTrack);
        }
      } else { // 之前采集现在不采集
        this._localVideoTrack.stopCapture();
      }
    } else if (capture) { // 之前不采集现在要采集
      const captureOption: CaptureOption = {};
      captureOption.deviceId = option?.cameraId || this._localVideoConfig.option?.cameraId;
      captureOption.facingMode = (option?.useFrontCamera || this._localVideoConfig.option?.useFrontCamera) ? NAME.FACING_MODE_USER : NAME.FACING_MODE_ENVIRONMENT;
      captureOption.customSource = option?.videoTrack || this._localVideoConfig.option?.videoTrack;
      await this._localVideoTrack.capture(captureOption);
    }
    if (option) {
      if (!isUndefined(option.profile)) {
        if (isString(option.profile)) {
          videoProfileMap[option.profile] && this._localVideoTrack.setProfile(videoProfileMap[option.profile]);
        } else {
          this._localVideoTrack.setProfile(option.profile);
        }
        if ((!option.cameraId || !this._localVideoTrack.isNeedToSwitchDevice(option.cameraId)) && isUndefined(option.useFrontCamera)) {
          // 不会重新进行 capture，需要应用新属性
          await this._localVideoTrack.applyProfile();
        }
      }

      if (!isUndefined(option.fillMode)) {
        playOption.objectFit = option.fillMode;
      }
      if (!isUndefined(option.mirror)) {
        playOption.mirror = option.mirror;
      }
      if (!isUndefined(option.rotation)) {
        this._localVideoTrack.setRotation(option.rotation);
      }
      if (option.qosPreference && this._localVideoTrack.mediaTrack) {
        this._localVideoTrack.setContentHint(getContentHintFromQosPreference(option.qosPreference));
      }
      if (!isUndefined(option.small)) {
        if (isBoolean(option.small) && !option.small) {
          this._localVideoTrack.stopSmall();
        } else {
          this._localVideoTrack.updateSmallConfig(convertVideoProfile(option.small, true));
        }
      }
    }
    // 没指定 publish 但指定了 capture 也可能要 publish
    if (this._room.isJoined && isUndefined(publish) && this._localVideoConfig.publish && capture && !this._localVideoConfig.capture) {
      this._room.publish(this._localVideoTrack).catch(() => { });
    }
    if (this._room.isJoined) {
      const needPublish = publish ?? this._localVideoConfig.publish;
      if (needPublish) {
        this._room.publish(this._localVideoTrack).catch(() => { });
      } else {
        this._room.unpublish(this._localVideoTrack).catch(() => { });
      }
    }
    if (!isUndefined(mute)) {
      await this._localVideoTrack.setMute(mute);
    }
    await this._updateVideoPlayOption({ view, playOption, track: this._localVideoTrack, prevConfig: this._localVideoConfig });

    deepMerge(this._localVideoConfig, config);
  }

  /**
   * Stop capturing, previewing, and publishing the local camera.
   * - If you only want to stop publishing video but keep the local camera preview, you can use the {@link TRTC#updateLocalVideo updateLocalVideo({ publish:false })} method.<br>
   * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * @example
   * await trtc.stopLocalVideo();
   */
  @randStop('video')
  @addAPICallLog()
  async stopLocalVideo() {
    if (!this._localVideoTrack) return;
    if (this._room.isJoined) {
      // unpublish 不会失败，catch error
      await this._room.unpublish(this._localVideoTrack).catch(() => { });
    }
    this._localVideoTrack.stop();
    this._localVideoTrack.close();
    eventManager.remove(this._localVideoTrack);
    this._localVideoTrack = null;
    this._localVideoConfig = null;
  }

  /**
   * @typedef {object|string} ScreenShareProfile - Screen sharing resolution, bit rate, and frame rate configuration
   * Screen sharing configuration parameters, can use preset values or custom resolution and other parameters
   * | Screen Profile | Resolution (width x height) | Frame Rate (fps) | Bitrate (kbps) |
   * | :---       | :---           | :---      | :---        |
   * | 480p       | 640 x 480      | 5         | 900         |
   * | 480p_2     | 640 x 480      | 30        | 1000        |
   * | 720p       | 1280 x 720     | 5         | 1200        |
   * | 720p_2     | 1280 x 720     | 30        | 3000        |
   * | 1080p      | 1920 x 1080    | 5         | 1600        |
   * | 1080p_2    | 1920 x 1080    | 30        | 4000        |
   * - The default resolution for screen sharing is `1080p`.
   * - If the above profiles do not meet your business needs, you can also specify custom resolution, frame rate, and bitrate.

   * @property {number} width - Screen sharing width
   * @property {number} height - Screen sharing height
   * @property {number} frameRate - Screen sharing frame rate
   * @property {number} bitrate - Screen sharing bitrate
   * @example
   * const config = {
   *  option: {
   *   profile: '720p',
   *  },
   * }
   * await trtc.startScreenShare(config);
   */
  /**
   * Start screen sharing.
   *
   * - After starting screen sharing, other users in the room will receive the {@link module:EVENT.REMOTE_VIDEO_AVAILABLE REMOTE_VIDEO_AVAILABLE} event, with streamType as {@link module:TYPE.STREAM_TYPE_SUB STREAM_TYPE_SUB}, and other users can play screen sharing through {@link TRTC#startRemoteVideo startRemoteVideo}.
   * @param {object} [config]
   * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - The HTMLElement instance or Id for previewing local screen sharing. If not passed or passed as null, local screen sharing will not be rendered.
   * @param {boolean} [config.publish] - Whether to publish screen sharing to the room. The default is true. If you call this interface before entering the room and publish = true, the SDK will automatically publish after entering the room. You can get the publish state by listening this event {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}.
   * @param {boolean} [config.muteSystemAudio=false] - Whether to mute system audio. Supported since v5.12.0+.
   * @param {object} [config.option] - Screen sharing configuration
   * @param {boolean} [config.option.systemAudio] - Whether to capture system audio. The default is false.
   * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - Video fill mode. The default is `contain`, refer to {@link https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit CSS object-fit} property.
   * @param {ScreenShareProfile} [config.option.profile] - Screen sharing encoding configuration. Default value is `1080p`.
   * @param {QOS_PREFERENCE_SMOOTH|QOS_PREFERENCE_CLEAR} [config.option.qosPreference] - Set the video encoding strategy for weak networks. Smooth first ({@link module:TYPE.QOS_PREFERENCE_SMOOTH QOS_PREFERENCE_SMOOTH}) or Clear first(default) ({@link module:TYPE.QOS_PREFERENCE_CLEAR QOS_ PREFERENCE_SMOOTH})
   * @param {HTMLElement} [config.option.captureElement] - Capture screen from the specified element of current tab. Available on Chrome 104+.
   * @param {'current-tab' | 'tab' | 'window' | 'monitor'} [config.option.preferDisplaySurface='monitor'] - The prefer display surface for screen sharing. Available on Chrome 94+.
   * - The default is monitor, which means that monitor capture will be displayed first in the Screen Sharing Capture pre-checkbox。
   * - If you fill in 'current-tab', the pre-checkbox will only show the current page.
   * @throws
   * - {@link module:ERROR_CODE.ENV_NOT_SUPPORTED ENV_NOT_SUPPORTED}
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
   * @example
   * // Start screen sharing
   * await trtc.startScreenShare();
   * @memberof TRTC
   */
  @validate(validateConfig.TRTC.startScreenShare)
  @randStart('screen', () => true)
  @addAPICallLog()
  async startScreenShare(config: ScreenShareConfig = { publish: true, view: null }) {
    if (this._localScreenTrack) {
      this._log.warn('screen share is already started');
      return;
    }

    const { view = null, publish = true, muteSystemAudio, option } = config;
    const localScreenTrack = new LocalScreenTrack(this._room.videoManager);
    localScreenTrack.on(LocalTrackEvent.PUBLISH_STATE_CHANGED, (event: LocalTrackEventTypes[LocalTrackEvent.PUBLISH_STATE_CHANGED][0]) => {
      let error;
      if (event.error) {
        error = RtcError.convertFrom(event.error);
      }
      this.emit(TRTCEvent.PUBLISH_STATE_CHANGED, { ...event, error });
    });
    localScreenTrack.once(TrackEvent.FIRST_VIDEO_FRAME, (data: FirstVideoFrameInfo) => {
      this.emit(TRTCEvent.FIRST_VIDEO_FRAME, {
        ...data,
        streamType: convertStreamType(data.streamType)
      });
    });
    this._listenOutputTrackChanged(localScreenTrack);
    // 支持设置屏幕分享从主流推流
    if (config.streamType === TRTCStreamType.Main) {
      localScreenTrack.mediaType = MediaType.BIG_VIDEO;
    }

    let localScreenAudioTrack: LocalScreenAudioTrack | null = null;

    const captureOption: ScreenCaptureOption = {};
    const playOption: PlayOption = {};

    if (option) {
      if (!isUndefined(option.profile)) {
        if (isString(option.profile)) {
          screenProfileMap[option.profile] && localScreenTrack.setProfile(screenProfileMap[option.profile]);
        } else {
          localScreenTrack.setProfile(option.profile);
        }
      }
      if (option.systemAudio) {
        captureOption.systemAudio = true;
        captureOption.echoCancellation = option.echoCancellation;
        captureOption.noiseSuppression = option.noiseSuppression;
        captureOption.autoGainControl = option.autoGainControl;
      }
      if (!isUndefined(option.fillMode)) {
        playOption.objectFit = option.fillMode;
      }
      if (option.videoTrack) {
        captureOption.videoTrack = option.videoTrack;
      }
      if (option.audioTrack) {
        captureOption.audioTrack = option.audioTrack;
      }
      if (option.captureElement) {
        captureOption.captureElement = option.captureElement;
      }
      if (option.preferDisplaySurface) {
        captureOption.preferDisplaySurface = option.preferDisplaySurface;
      }

      if (option.qosPreference) {
        captureOption.contentHint = getContentHintFromQosPreference(option.qosPreference);
      }
    }

    const mediaStream = await localScreenTrack.capture(captureOption);

    localScreenTrack.mediaTrack!.addEventListener(NAME.ENDED, () => {
      // 用户点击浏览器提供的关闭屏幕分享按钮，SDK 自动取消推流关闭屏幕分享。
      this._stopScreenShare();
      this.emit(TRTCEvent.SCREEN_SHARE_STOPPED);
    });
    if (mediaStream.getAudioTracks()[0]) {
      localScreenAudioTrack = new LocalScreenAudioTrack(this._room.audioManager);
      await localScreenAudioTrack.setInputMediaStreamTrack(mediaStream.getAudioTracks()[0]);
      if (isBoolean(muteSystemAudio) && localScreenAudioTrack.mediaTrack) {
        localScreenAudioTrack.mediaTrack.enabled = !muteSystemAudio;
      }
      if (this._speakerId) localScreenAudioTrack.setAudioOutput(this._speakerId);
    }

    eventManager.create(localScreenTrack, localScreenTrack)
      .add(TrackEvent.PLAYER_STATE_CHANGED, (event: any) => {
        this.emit(TRTCEvent.VIDEO_PLAY_STATE_CHANGED, { ...event, userId: '', streamType: TRTCStreamType.Sub });
      });
    if (publish && this._room.isJoined) {
      const trackListToPublish: ILocalTrack[] = [localScreenTrack];
      if (localScreenAudioTrack) {
        trackListToPublish.push(localScreenAudioTrack);
        this._checkScreenAudioEchoCancellation(localScreenTrack, localScreenAudioTrack);
      }
      this._room.publish(...trackListToPublish).catch(() => { });
    }

    this._localScreenTrack = localScreenTrack;
    this._localScreenAudioTrack = localScreenAudioTrack;
    this._localScreenConfig = { ...config, view, publish };

    await this._updateVideoPlayOption({ view, playOption, track: localScreenTrack });
  }

  /**
   * Update screen sharing configuration
   * - This interface needs to be called after {@link TRTC#startScreenShare startScreenShare()} is successful.
   * - This interface can be called multiple times.
   * - This method uses incremental update: only update the passed-in parameters, and keep the parameters that are not passed-in unchanged.
   * @param {object} [config]
   * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - The HTMLElement instance or Id for screen sharing preview. If not passed in or passed in null, the screen sharing will not be rendered.
   * @param {boolean} [config.publish=true] - Whether to publish screen sharing to the room
   * @param {boolean} [config.muteSystemAudio=false] - Whether to mute system audio. Supported since v5.12.0+.
   * @param {object} [config.option] - Screen sharing configuration
   * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - Video fill mode. The default is `contain`, refer to {@link https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit CSS object-fit} property.
   * @param {QOS_PREFERENCE_SMOOTH|QOS_PREFERENCE_CLEAR} [config.option.qosPreference] - Set the video encoding strategy for weak networks. Smooth first ({@link module:TYPE.QOS_PREFERENCE_SMOOTH QOS_PREFERENCE_SMOOTH}) or Clear first ({@link module:TYPE.QOS_PREFERENCE_CLEAR QOS_ PREFERENCE_SMOOTH})
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
   * @example
   * // Stop screen sharing, but keep the local preview of screen sharing
   * await trtc.updateScreenShare({ publish:false });
   * @memberof TRTC
   */
  @validate(validateConfig.TRTC.updateScreenShare)
  @randUpdate('screen')
  @addAPICallLog()
  async updateScreenShare(config: UpdateScreenShareConfig) {
    if (!this._localScreenTrack || !this._localScreenConfig) return;
    const { view, publish, muteSystemAudio, option } = config;
    const playOption: PlayOption = {};

    if (option) {
      if (!isUndefined(option.fillMode)) {
        playOption.objectFit = option.fillMode;
      }
      if (option.qosPreference) {
        const contentHint = getContentHintFromQosPreference(option.qosPreference);
        this._localScreenTrack.setContentHint(contentHint);
      }
    }

    if (this._room.isJoined && !isUndefined(publish)) {
      if (publish && !this._localScreenConfig.publish) {
        this._room.publish(this._localScreenTrack).catch(() => { });
        if (this._localScreenAudioTrack) this._room.publish(this._localScreenAudioTrack).catch(() => { });
      }
      if (this._localScreenConfig.publish && !publish) {
        this._room.unpublish(this._localScreenTrack).catch(() => { });
        if (this._localScreenAudioTrack) this._room.unpublish(this._localScreenAudioTrack).catch(() => { });
      }
    }
    if (this._localScreenAudioTrack?.mediaTrack && isBoolean(muteSystemAudio)) {
      this._localScreenAudioTrack.mediaTrack.enabled = !muteSystemAudio;
    }
    await this._updateVideoPlayOption({ view, playOption, track: this._localScreenTrack, prevConfig: this._localScreenConfig });

    deepMerge(this._localScreenConfig, config);
  }

  /**
   * Stop screen sharing.

   * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * @example
   * await trtc.stopScreenShare();
   */
  @addAPICallLog()
  async stopScreenShare() {
    return await this._stopScreenShare();
  }

  /**
   * Play remote video
   *
   * - When to call: Call after receiving the {@link module:EVENT.REMOTE_VIDEO_AVAILABLE TRTC.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE)} event.
   * @param {object} [config]
   * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - The HTMLElement instance or Id used to play remote video. If not passed or passed null, the video will not be rendered, but the bandwidth will still be consumed.
   * @param {string} config.userId - Remote user ID
   * @param {TRTC.TYPE.STREAM_TYPE_MAIN|TRTC.TYPE.STREAM_TYPE_SUB} config.streamType - Remote stream type
   * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: Main stream (remote user's camera)
   * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: Sub stream (remote user's screen sharing)
   * @param {object} [config.option] - Remote video configuration
   * @param {boolean} [config.option.small] - Whether to subscribe small streams
   * @param {boolean} [config.option.mirror] - Whether to enable mirror
   * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - Video fill mode. Refer to the {@link https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit CSS object-fit} property.
   * @param {boolean} [config.option.receiveWhenViewVisible] - Since v5.4.0 <br>Subscribe video only when view is visible. Refer to: {@tutorial 27-advanced-small-stream}.
   * @param {HTMLElement} [config.option.viewRoot=document.body] - Since v5.4.0 <br>The root element is the parent element of the view and is used to calculate whether the view is visible relative to the root. The default value is document.body, and it is recommended that you use the first-level parent of the video view list. Refer to: {@tutorial 27-advanced-small-stream}.
   * @param {string} [config.option.poster] - Since v5.10.0 <br> The default poster image for the video. Generally no need to concern about this, only a few browsers display their default built-in images, in which case you can customize it through this parameter. Reference: {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLVideoElement/poster HTMLVideoElement: poster property}
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.INVALID_OPERATION INVALID_OPERATION}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
   * @example
   * trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
   *   // You need to place the video container in the DOM in advance, and it is recommended to use `${userId}_${streamType}` as the element id.
   *   trtc.startRemoteVideo({ userId, streamType, view: `${userId}_${streamType}` });
   * })
   * @memberof TRTC
   */
  @validate(validateConfig.TRTC.startRemoteVideo)
  @randStart((config: RemoteVideoConfig) => `v${config.userId}${config.streamType}`, () => true)
  @addAPICallLog({ getRemoteId: (config: RemoteVideoConfig) => `${config.userId}_${config.streamType}` })
  async startRemoteVideo(config: RemoteVideoConfig) {
    const { view, userId, streamType, option } = config;
    const key = `${userId}_${streamType}`;
    if (this._remoteVideoConfigMap.has(key)) {
      this._log.warn(`remote video has already started. userId:${userId}, streamType:${streamType}`);
      return;
    }
    const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
    if (!remotePublishedUser) return;
    const playOption: PlayOption = {};
    const remoteTrack = streamType === TRTCStreamType.Main ? remotePublishedUser.remoteVideoTrack : remotePublishedUser.remoteAuxiliaryTrack;
    remoteTrack.on(RemoteTrackEvent.DECODE_FAILED, (error: any) => {
      this.emit(TRTCEvent.ERROR, new RtcError({
        code: ErrorCode.OPERATION_FAILED,
        extraCode: ErrorCodeDictionary.VIDEO_DECODE_FAILED,
        message: 'video decode failed'
      }));
    });
    this._listenOutputTrackChanged(remoteTrack);
    if (option) {
      if (!isUndefined(option.fillMode)) {
        playOption.objectFit = option.fillMode;
      }
      if (!isUndefined(option.mirror)) {
        playOption.mirror = option.mirror;
      }
      if (!isUndefined(option.poster)) {
        playOption.poster = option.poster;
      }
      playOption.canvasRender = option.canvasRender;
      if (streamType === TRTCStreamType.Main && !isUndefined(option.small)) {
        if (!remotePublishedUser.remoteVideoTrack.isSubscribing && !remotePublishedUser.remoteVideoTrack.isSubscribed) remotePublishedUser.remoteVideoTrack.setMediaType(option.small ? MediaType.SMALL_VIDEO : MediaType.BIG_VIDEO);
        this._room.changeType(option.small, remoteTrack.user);
      }
    }
    await this._room.subscribe(remoteTrack);
    await this._enableVideoDecodeFallback(remoteTrack, streamType);
    await this._updateVideoPlayOption({ view, playOption, track: remoteTrack });
    this._emitTrackEvent(remoteTrack);

    this._remoteVideoConfigMap.set(key, { config });
    if (option && !isUndefined(option.receiveWhenViewVisible)) {
      this._observeView({ remoteTrack, view, receiveWhenViewVisible: option.receiveWhenViewVisible, viewRoot: option?.viewRoot });
    }
  }

  /**
   * Update remote video playback configuration<br>
   * - This method should be called after {@link TRTC#startRemoteVideo startRemoteVideo} is successful.
   * - This method can be called multiple times.
   * - This method uses incremental updates, so only the configuration items that need to be updated need to be passed in.
   * @param {object} [config]
   * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - The HTMLElement instance or Id used to play remote video. If not passed or passed null, the video will not be rendered, but the bandwidth will still be consumed.
   * @param {string} config.userId - Remote user ID
   * @param {TRTC.TYPE.STREAM_TYPE_MAIN|TRTC.TYPE.STREAM_TYPE_SUB} config.streamType - Remote stream type
   * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: Main stream (remote user's camera)
   * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: Sub stream (remote user's screen sharing)
   * @param {object} [config.option] - Remote video configuration
   * @param {boolean} [config.option.small] - Whether to subscribe small streams. Refer to: {@tutorial 27-advanced-small-stream}.
   * @param {boolean} [config.option.mirror] - Whether to enable mirror
   * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - Video fill mode. Refer to the {@link https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit CSS object-fit} property.
   * @param {boolean} [config.option.receiveWhenViewVisible] - Since v5.4.0 <br>Subscribe video only when view is visible. Refer to: {@tutorial 27-advanced-small-stream}.
   * @param {HTMLElement} [config.option.viewRoot=document.body] - Since v5.4.0 <br>The root element is the parent element of the view and is used to calculate whether the view is visible relative to the root. The default value is document.body, and it is recommended that you use the first-level parent of the video view list. Refer to: {@tutorial 27-advanced-small-stream}.
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.INVALID_OPERATION INVALID_OPERATION}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * @example
   * const config = {
   *  view: document.getElementById(userId), // you can use a new view to update the position of video.
   *  userId,
   *  streamType: TRTC.TYPE.STREAM_TYPE_MAIN
   * }
   * await trtc.updateRemoteVideo(config);
   * @memberof TRTC
   */
  @validate(validateConfig.TRTC.updateRemoteVideo)
  @randUpdate((config: RemoteVideoConfig) => `v${config.userId}${config.streamType}`)
  @addAPICallLog({ getRemoteId: (config: RemoteVideoConfig) => `${config.userId}_${config.streamType}` })
  async updateRemoteVideo(config: RemoteVideoConfig) {
    const { view, userId, streamType, option } = config;
    const key = `${userId}_${streamType}`;
    const prevData = this._remoteVideoConfigMap.get(key);
    if (!prevData || !this._room.remotePublishedUserMap.has(userId)) return;
    const playOption: PlayOption = {};

    if (option) {
      if (!isUndefined(option.fillMode)) {
        playOption.objectFit = option.fillMode;
      }
      if (!isUndefined(option.mirror)) {
        playOption.mirror = option.mirror;
      }
    }
    let remoteTrack: RemoteVideoTrack | RemoteAuxiliaryTrack | null = null;
    const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
    if (streamType === TRTCStreamType.Main && remotePublishedUser?.muteState.hasVideo) {
      remoteTrack = remotePublishedUser.remoteVideoTrack;
    }
    if (streamType === TRTCStreamType.Sub && remotePublishedUser?.muteState.hasAuxiliary) {
      remoteTrack = remotePublishedUser.remoteAuxiliaryTrack;
    }
    if (!remoteTrack) return;
    const { config: prevRemoteConfig } = prevData;

    // 大小流切换
    if (streamType === TRTCStreamType.Main && option && !isUndefined(option.small)) {
      this._room.changeType(option.small, remoteTrack.user);
    }
    await this._updateVideoPlayOption({ view, playOption, track: remoteTrack, prevConfig: prevRemoteConfig });
    deepMerge(prevRemoteConfig, config);
    const receiveWhenViewVisible = isUndefined(option?.receiveWhenViewVisible) ? prevRemoteConfig.option?.receiveWhenViewVisible : option.receiveWhenViewVisible;
    const viewToObserve = isUndefined(view) ? prevRemoteConfig.view : view;
    const viewRoot = isUndefined(option?.viewRoot) ? prevRemoteConfig.option?.viewRoot : option.viewRoot;
    this._observeView({ remoteTrack, view: viewToObserve, receiveWhenViewVisible, viewRoot });
  }

  /**
   * Used to stop remote video playback.<br>
   * @param {object} config - Remote video configuration
   * @param {string} config.userId - Remote user ID, '*' represents all users.
   * @param {TRTC.TYPE.STREAM_TYPE_MAIN|TRTC.TYPE.STREAM_TYPE_SUB} [config.streamType] - Remote stream type. This field is required when userId is not '*'.
   * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: Main stream (remote user's camera)
   * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: Sub stream (remote user's screen sharing)
   * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * @example
   * // Stop playing all remote users
   * await trtc.stopRemoteVideo({ userId: '*' });
   */
  @validate(validateConfig.TRTC.stopRemoteVideo)
  @createDecorator<TRTC>(next => async function (this: TRTC, config: StopRemoteVideoConfig) {
    if (config.userId === '*') {
      const promises: Promise<any>[] = [];
      this._room.remotePublishedUserMap.forEach(user => {
        if (this._remoteVideoConfigMap.has(`${user.userId}_${TRTCStreamType.Main}`)) promises.push(this.stopRemoteVideo({ streamType: TRTCStreamType.Main, userId: user.userId }).catch(() => { }));
        if (this._remoteVideoConfigMap.has(`${user.userId}_${TRTCStreamType.Sub}`)) promises.push(this.stopRemoteVideo({ streamType: TRTCStreamType.Sub, userId: user.userId }).catch(() => { }));
      });
      return Promise.all(promises);
    }
    return next.call(this, config);
  })
  @addAPICallLog({ getRemoteId: (config: RemoteVideoConfig) => `${config.userId}_${config.streamType}` })
  async stopRemoteVideo(config: StopRemoteVideoConfig) {
    return this._stopRemoteVideo(config);
  }

  @randStop((config: StopRemoteVideoConfig) => `v${config.userId}${config.streamType}`)
  private async _stopRemoteVideo(config: StopRemoteVideoConfig, callUnsubscribe = true) {
    const remoteVideoTrackList: RemoteVideoTrack[] = [];
    const remotePublishedUser = this._room.remotePublishedUserMap.get(config.userId);
    if (remotePublishedUser) {
      const { muteState, remoteVideoTrack, remoteAuxiliaryTrack } = remotePublishedUser;
      if (config.streamType === TRTCStreamType.Main) {
        remoteVideoTrack.stop();
        if (muteState.hasVideo) {
          remoteVideoTrackList.push(remoteVideoTrack);
        }
      }
      if (config.streamType === TRTCStreamType.Sub) {
        remoteAuxiliaryTrack.stop();
        if (muteState.hasAuxiliary) {
          remoteVideoTrackList.push(remoteAuxiliaryTrack);
        }
      }
    }
    for (const remoteTrack of remoteVideoTrackList) {
      if (callUnsubscribe) {
        await this._room.unsubscribe(remoteTrack);
        this._mediaTrackMap.delete(remoteTrack.outMediaTrack!);
      }
    }

    this._removeRemoteVideoConfig(config.userId, config.streamType);
  }

  _removeRemoteVideoConfig(userId: string, streamType?: TRTCStreamType) {
    const key = `${userId}_${streamType}`;
    const data = this._remoteVideoConfigMap.get(key);
    if (data && data.observer) {
      data.observer.disconnect();
    }
    this._remoteVideoConfigMap.delete(key);
  }

  /**
   * Mute a remote user and stop subscribing audio data from that user. Only effective for the current user, other users in the room can still hear the muted user's voice.<br>
   *
   * Note:
   * - By default, after entering the room, the SDK will automatically play remote audio. You can call this interface to mute or unmute remote users.
   * - If the parameter autoReceiveAudio = false is passed in when entering the room, remote audio will not be played automatically. When audio playback is required, you need to call this method (mute is passed in false) to play remote audio.
   * - This interface is effective before or after entering the room (enterRoom), and the mute state will be reset to false after exiting the room (exitRoom).
   * - If you want to continue subscribing audio data from the user but not play it, you can call setRemoteAudioVolume(userId, 0)
   * @param {string} userId - Remote user ID, '*' represents all users.
   * @param {boolean} mute - Whether to mute
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.INVALID_OPERATION INVALID_OPERATION}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * @example
   * // Mute all remote users
   * await trtc.muteRemoteAudio('*', true);
   */
  @validate(...validateConfig.TRTC.muteRemoteAudio)
  @addAPICallLog({ getRemoteId: (userId: string) => userId })
  async muteRemoteAudio(userId: string, mute: boolean) {
    this._remoteAudioMuteMap.set(userId, mute);
    try {
      if (userId === '*') {
        if (mute) {
          await this._stopRemoteAudio({ userId });
        } else {
          const remotePublishedUserList = [...this._room.remotePublishedUserMap.values()];
          for (const remotePublishedUser of remotePublishedUserList) {
            if (remotePublishedUser.muteState.hasAudio && !this._remoteAudioConfigMap.has(remotePublishedUser.userId)) {
              await this._startRemoteAudio({ userId: remotePublishedUser.userId });
            }
          }
        }
      } else {
        if (mute) {
          await this._stopRemoteAudio({ userId });
        } else if (!this._remoteAudioConfigMap.has(userId)) {
          await this._startRemoteAudio({ userId });
        }
      }
    } catch (error) {
      this._remoteAudioMuteMap.delete(userId);
      throw error;
    }
  }

  /**
   * Used to control the playback volume of remote audio. If a remote user re-enters the room, the volume needs to be set again.<br>
   *
   * - Not supported by iOS Safari
   * @param {string} userId - Remote user ID. '*' represents all remote users.
   * @param {number} volume - Volume, ranging from 0 to 100. The default value is 100.<br>
   * Since `v5.1.3+`, the volume can be set higher than 100.
   * @example
   * await trtc.setRemoteAudioVolume('123', 90);
   */
  @validateSync(...validateConfig.TRTC.setRemoteAudioVolume)
  @debounce(200, (userId: string) => userId)
  @addAPICallLog({ getRemoteId: (userId: string) => userId })
  setRemoteAudioVolume(userId: string, volume: number) {
    if (userId === '*') {
      const remotePublishedUserList = [...this._room.remotePublishedUserMap.values()];
      for (const remotePublishedUser of remotePublishedUserList) {
        this._remoteAudioVolumeMap.set(remotePublishedUser.userId, volume);
        if (remotePublishedUser.remoteAudioTrack.isSubscribed) {
          this._updateAudioPlayOption({ playOption: { volume }, track: remotePublishedUser.remoteAudioTrack });
        }
      }
    } else if (userId) {
      const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
      this._remoteAudioVolumeMap.set(userId, volume);
      if (remotePublishedUser && remotePublishedUser.remoteAudioTrack.isSubscribed) {
        this._updateAudioPlayOption({ playOption: { volume }, track: remotePublishedUser.remoteAudioTrack });
      }
    }
  }

  startPlugin<T extends keyof PluginStartOptionsMap, O extends PluginStartOptionsMap[T]>(plugin: O extends undefined ? never : T, options: O): Promise<any>;
  startPlugin<T extends keyof PluginStartOptionsMap, O extends PluginStartOptionsMap[T]>(plugin: O extends undefined ? T : never): Promise<any>;

  /**
   * start plugin
   *
   * | pluginName | name | tutorial | param |
   * | --- | --- | --- | --- |
   * | 'AudioMixer' | Audio Mixer Plugin | {@tutorial 22-advanced-audio-mixer} | [AudioMixerOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#AudioMixerOptions) |
   * | 'AIDenoiser' | AI Denoiser Plugin | {@tutorial 35-advanced-ai-denoiser} | [AIDenoiserOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#AIDenoiserOptions) |
   * | 'VirtualBackground' | Virtual Background Plugin | {@tutorial 36-advanced-virtual-background} | [VirtualBackgroundOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#VirtualBackgroundOptions) |
   * | 'Watermark' | Watermark Plugin | {@tutorial 29-advanced-water-mark} | [WatermarkOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#WatermarkOptions) |
   * | 'SmallStreamAutoSwitcher' | Small Stream Auto Switcher Plugin | {@tutorial 41-advanced-small-stream-auto-switcher} | [SmallStreamAutoSwitcherOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#SmallStreamAutoSwitcherOptions) |
   *
   * @param {PluginName} plugin
   * @param {AudioMixerOptions|AIDenoiserOptions|VirtualBackgroundOptions|WatermarkOptions|SmallStreamAutoSwitcherOptions} options
   * @returns {Promise<void>}
   */
  @validatePlugin('start')
  @after(plugin => plugin.afterStart?.())
  @randStart((plugin: IPlugin, options: any) => (plugin.disableRandomCall ? null : plugin.getAlias() + plugin.getGroup(options)))
  @addAPICallLog({
    replaceArg: (plugin: IPlugin) => ({ argIndex: 0, value: plugin.getName() }),
    getKVReportKey: (plugin: IPlugin) => KV_REPORT_KEY_START_PLUGIN[plugin.getName() as keyof typeof KV_REPORT_KEY_START_PLUGIN],
    ignoreLog: (plugin: IPlugin) => plugin.getName() === 'Debug',
    ignoreErrorLog: (plugin: IPlugin) => plugin.getName() === 'AudioProcessor'
  })
  async startPlugin<T extends keyof PluginStartOptionsMap>(plugin: T, options?: PluginStartOptionsMap[T]) {
    // 装饰器改变了参数类型，但是生成声明文件需要原始类型
    return (plugin as unknown as IPlugin).start(options);
  }

  updatePlugin<T extends keyof PluginUpdateOptionsMap, O extends PluginUpdateOptionsMap[T]>(plugin: O extends undefined ? never : T, options: O): Promise<any>;
  updatePlugin<T extends keyof PluginUpdateOptionsMap, O extends PluginUpdateOptionsMap[T]>(plugin: O extends undefined ? T : never): Promise<any>;
  /**
   * Update plugin
   *
   * | pluginName | name | tutorial | param |
   * | --- | --- | --- | --- |
   * | 'AudioMixer' | Audio Mixer Plugin | {@tutorial 22-advanced-audio-mixer} | [UpdateAudioMixerOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#UpdateAudioMixerOptions) |
   * | 'VirtualBackground' | Virtual Background Plugin | {@tutorial 36-advanced-virtual-background} | [VirtualBackgroundOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#VirtualBackgroundOptions) |
   *
   * @param {PluginName} plugin
   * @param {UpdateAudioMixerOptions} options
   * @returns {Promise<void>}
   */
  @validatePlugin('update')
  @randUpdate((plugin: IPlugin, options: any) => (plugin.disableRandomCall ? null : plugin.getAlias() + plugin.getGroup(options)), { merge: (arg1, arg2: any) => (deepMerge(arg1[1], arg2[1]), arg1) })
  @addAPICallLog({
    replaceArg: (plugin: IPlugin) => ({ argIndex: 0, value: plugin.getName() }),
    getKVReportKey: (plugin: IPlugin) => KV_REPORT_KEY_UPDATE_PLUGIN[plugin.getName() as keyof typeof KV_REPORT_KEY_UPDATE_PLUGIN]
  })
  async updatePlugin<T extends keyof PluginUpdateOptionsMap>(plugin: T, options?: PluginUpdateOptionsMap[T]) {
    // 装饰器改变了参数类型，但是生成声明文件需要原始类型
    return (plugin as unknown as IPlugin).update(options);
  }

  stopPlugin<T extends keyof PluginStopOptionsMap, O extends PluginStopOptionsMap[T]>(plugin: O extends undefined ? never : T, options: O): Promise<any>;
  stopPlugin<T extends keyof PluginStopOptionsMap, O extends PluginStopOptionsMap[T]>(plugin: O extends undefined ? T : never): Promise<any>;
  /**
   * Stop plugin
   *
   * | pluginName | name | tutorial | param |
   * | --- | --- | --- | --- |
   * | 'AudioMixer' | Audio Mixer Plugin | {@tutorial 22-advanced-audio-mixer} | [StopAudioMixerOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#StopAudioMixerOptions) |
   * | 'AIDenoiser' | AI Denoiser Plugin | {@tutorial 35-advanced-ai-denoiser} |  |
   * | 'Watermark' | Watermark Plugin | {@tutorial 29-advanced-water-mark} |  |
   * | 'SmallStreamAutoSwitcher' | Small Stream Auto Switcher Plugin | {@tutorial 41-advanced-small-stream-auto-switcher} |  |
   *
   * @param {PluginName} plugin
   * @param {StopAudioMixerOptions=} options
   * @returns {Promise<void>}
   */
  @validatePlugin('stop')
  @randStop((plugin: IPlugin, options: any) => {
    if (plugin.disableRandomCall) return null;
    const group = plugin.getGroup(options);
    const alias = plugin.getAlias();
    return group === '*' ? new RegExp(`${alias}.*`) : alias + group;
  })
  @addAPICallLog({
    replaceArg: (plugin: IPlugin) => ({ argIndex: 0, value: plugin.getName() }),
    getKVReportKey: (plugin: IPlugin) => KV_REPORT_KEY_STOP_PLUGIN[plugin.getName() as keyof typeof KV_REPORT_KEY_STOP_PLUGIN]
  })
  async stopPlugin<T extends keyof PluginStopOptionsMap>(plugin: T, options?: PluginStopOptionsMap[T]) {
    // 装饰器改变了参数类型，但是生成声明文件需要原始类型
    return (plugin as unknown as IPlugin).stop(options);
  }
  /**
   * Enables or disables the volume callback.<br>
   *
   * - After enabling this function, whether someone is speaking in the room or not, the SDK will regularly throw the {@link module:EVENT.AUDIO_VOLUME TRTC.on(TRTC.EVENT.AUDIO_VOLUME)} event, which feedbacks the volume evaluation value of each user.<br>
   *
   * @param {number} [interval=2000] Used to set the time interval for triggering the volume callback event. The default is 2000(ms), and the minimum value is 100(ms). If set to less than or equal to 0, the volume callback will be turned off.
   * @param {boolean} [enableInBackground=false] For performance reasons, when the page switches to the background, the SDK will not throw volume callback events. If you need to receive volume callback events when the page is switched to the background, you can set this parameter to true.
   * @memberof TRTC
   * @example
   * trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => {
   *    event.result.forEach(({ userId, volume }) => {
   *        const isMe = userId === ''; // When userId is an empty string, it represents the local microphone volume.
   *        if (isMe) {
   *            console.log(`my volume: ${volume}`);
   *        } else {
   *            console.log(`user: ${userId} volume: ${volume}`);
   *        }
   *    })
   * });
   *
   * // Enable volume callback and trigger the event every 1000ms
   * trtc.enableAudioVolumeEvaluation(1000);
   *
   * // To turn off the volume callback, pass in an interval value less than or equal to 0
   * trtc.enableAudioVolumeEvaluation(-1);
   */
  @validateSync(...validateConfig.TRTC.enableAudioVolumeEvaluation)
  enableAudioVolumeEvaluation(interval = 2000, enableInBackground = false) {
    this._room.enableAudioVolumeEvaluation(interval, enableInBackground);
  }

  /**
   * Listen to TRTC events<br><br>
   * For a detailed list of events, please refer to: {@link module:EVENT TRTC.EVENT}
   *
   * @param {string} eventName Event name
   * @param {function} handler Event callback function
   * @param {context} context Context
   * @memberof TRTC
   * @example
   * trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, event => {
   *   // REMOTE_VIDEO_AVAILABLE event handler
   * });
   */
  on<T extends keyof TRTCEventTypes>(event: T, handler: (...args: TRTCEventTypes[T]) => void, context?: any) {
    if (this.listeners(event).includes(handler)) return this;
    this._log.debug('on', event);
    super.on(event, handler, context);
    this._eventListened.add(event);
    if (this.listeners(TRTCEvent.AUDIO_FRAME).length > 0) {
      const { audioFrameEventConfigMap } = this.room.audioManager;
      if (!audioFrameEventConfigMap.get('')) {
        audioFrameEventConfigMap.set('', { enable: true });
      }
      if (this._localAudioTrack) this.room.audioManager.handleLocalTrackStarted({ userId: '', room: this.room });
    }
    return this;
  }

  emit<T extends keyof TRTCEventTypes>(event: T, ...args: ArgumentMap<TRTCEventTypes>[Extract<T, keyof TRTCEventTypes>]): boolean {
    try {
      // @ts-ignore
      if (TRTCEventSetToLogEmit.has(event)) {
        this._log.debug(`emit ${event} ${JSON.stringify(args)}`);
      }
    } catch (error) { }
    return super.emit(event, ...args);
  }

  /**
   * Remove event listener<br>
   *
   * @param {string} eventName Event name. Passing in the wildcard '*' will remove all event listeners.
   * @param {function} handler Event callback function
   * @param {context} context Context
   * @memberof TRTC
   * @example
   * trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, function peerJoinHandler(event) {
   *   // REMOTE_USER_ENTER event handler
   *   console.log('remote user enter');
   *
   *   trtc.off(TRTC.EVENT.REMOTE_USER_ENTER, peerJoinHandler);
   * });
   *
   * // Remove all event listeners
   * trtc.off('*');
   */
  // @ts-ignore
  off<T extends keyof TRTCEventTypes>(event: T | '*', handler: T extends '*' ? never : (...args: TRTCEventTypes[T]) => void, context?: any) {
    this._log.debug('off', event);
    if (event === '*') {
      this._eventListened.clear();
      this.removeAllListeners();
    } else {
      // @ts-ignore
      super.off(event, handler, context);
    }
    if ((event === TRTC.EVENT.AUDIO_FRAME || event === '*') && this.listeners(TRTC.EVENT.AUDIO_FRAME).length === 0) {
      const { getPCMAbortCtrlMap } = this.room.audioManager;
      getPCMAbortCtrlMap.forEach(value => {
        value?.abort('off');
      });
      getPCMAbortCtrlMap.clear();
    }
    return this;
  }

  /**
   * Get audio track
   *
   * @returns {MediaStreamTrack?} Audio track
   * @param {Object|string} [config] If not passed, get the local microphone audioTrack
   * @param {string} [config.userId] If not passed or passed an empty string, get the local audioTrack. Pass the userId of the remote user to get the remote user's audioTrack.
   * @param {STREAM_TYPE_MAIN|STREAM_TYPE_SUB} [config.streamType] - stream type:
   * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: Main stream (user's microphone)(default)
   * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: Sub stream (user's screen sharing audio). Only works for local screen sharing audio because there is only one remote audioTrack, and there is no distinction between Main and Sub for remote audioTrack.
   * @param {boolean} [config.processed=false] - Whether to get the processed audioTrack. The processed audioTrack is the audioTrack after the SDK processes the audio frame, such as ai-denose, gain, mix. The default value is false.
   * @memberof TRTC
   * @example
   * // Version before v5.4.3
   * trtc.getAudioTrack(); // Get local microphone audioTrack, captured by trtc.startLocalAudio()
   * trtc.getAudioTrack('remoteUserId'); // Get remote audioTrack
   *
   * // Since v5.4.3+, you can get local screen audioTrack by passing the streamType = TRTC.STREAM_TYPE_SUB
   * trtc.getAudioTrack({ streamType: TRTC.STREAM_TYPE_SUB });
   *
   * // Since v5.8.2+, you can get the processed audioTrack by passing processed = true
   * trtc.getAudioTrack({ processed: true });
   */
  getAudioTrack(configOrUserId: { userId?: string; streamType?: TRTCStreamType; processed?: boolean; } | string = { userId: '', streamType: TRTCStreamType.Main }): MediaStreamTrack | null {
    let userId;
    let theTrack: ITrack | null = null;
    let streamType = TRTCStreamType.Main;
    let processed = false;
    if (isString(configOrUserId)) userId = configOrUserId;
    else {
      userId = configOrUserId.userId;
      processed = configOrUserId.processed === true;
      if (configOrUserId.streamType) streamType = configOrUserId.streamType;
    }
    if (userId) {
      const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
      if (remotePublishedUser) theTrack = remotePublishedUser.remoteAudioTrack;
    } else {
      theTrack = streamType === TRTCStreamType.Sub ? this._localScreenAudioTrack : this._localAudioTrack;
    }
    // clone 参考 video ，保持对称。
    if (theTrack) return processed && theTrack.outMediaTrack && theTrack.outMediaTrack !== theTrack.mediaTrack ? theTrack.outMediaTrack.clone() : theTrack.mediaTrack;
    return null;
  }

  /**
   * Get video track
   *
   * @param {string} [config] If not passed, get the local camera videoTrack
   * @param {string} [config.userId] If not passed or passed an empty string, get the local videoTrack. Pass the userId of the remote user to get the remote user's videoTrack.
   * @param {STREAM_TYPE_MAIN|STREAM_TYPE_SUB} [config.streamType] - stream type:
   * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: Main stream (user's camera)(default)
   * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: Sub stream (user's screen sharing)
   * @param {boolean} [config.processed=false] - Whether to get the processed videoTrack. The processed videoTrack is the videoTrack after the SDK processes the video frame, such as virtual background, mirror, watermark, rotation. The default value is false.
   * @returns {MediaStreamTrack|null} Video track
   * @memberof TRTC
   * @example
   * // Get local camera videoTrack
   * const videoTrack = trtc.getVideoTrack();
   * // Get local screen sharing videoTrack
   * const screenVideoTrack = trtc.getVideoTrack({ streamType: TRTC.TYPE.STREAM_TYPE_SUB });
   * // Get remote user's main stream videoTrack
   * const remoteMainVideoTrack = trtc.getVideoTrack({ userId: 'test', streamType: TRTC.TYPE.STREAM_TYPE_MAIN });
   * // Get remote user's sub stream videoTrack
   * const remoteSubVideoTrack = trtc.getVideoTrack({ userId: 'test', streamType: TRTC.TYPE.STREAM_TYPE_SUB });
   * // Since v5.8.2+, you can get the processed videoTrack by passing processed = true
   * const processedVideoTrack = trtc.getVideoTrack({ processed: true });
  */
  getVideoTrack(config: { userId?: string; streamType?: TRTCStreamType; processed?: boolean; } = { userId: '', streamType: TRTCStreamType.Main }): MediaStreamTrack | null {
    const { userId = '', streamType = TRTCStreamType.Main, processed = false } = config;
    let theTrack: ITrack | null = null;
    if (userId === '') {
      if (streamType === TRTCStreamType.Main && this._localVideoTrack) {
        theTrack = this._localVideoTrack;
      }
      if (streamType === TRTCStreamType.Sub && this._localScreenTrack) {
        theTrack = this._localScreenTrack;
      }
    } else {
      const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
      if (remotePublishedUser) {
        theTrack = streamType === TRTCStreamType.Main ? remotePublishedUser.remoteVideoTrack : remotePublishedUser.remoteAuxiliaryTrack;
      }
    }
    // canvas capture 出来的 track 如果放入多个 video 标签，当其中一个 video 的 srcObject 设置成 null 时会导致 canvas 渲染闪烁（两帧切换），clone 后无这个问题。
    if (theTrack) return processed && theTrack.outMediaTrack && theTrack.outMediaTrack !== theTrack.mediaTrack ? theTrack.outMediaTrack.clone() : theTrack.mediaTrack;
    return null;
  }

  /**
   * Get video snapshot <br>
   * Notice: must play the video before it can obtain the snapshot. If there is no playback, an empty string will be returned.
   * @param {string} config.userId - Remote user ID
   * @param {TRTC.TYPE.STREAM_TYPE_MAIN|TRTC.TYPE.STREAM_TYPE_SUB} config.streamType
   * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: Main stream
   * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: Sub stream
   * @since 5.4.0
   * @example
   * // get self main stream video frame
   * trtc.getVideoSnapshot()
   * // get self sub stream video frame
   * trtc.getVideoSnapshot({streamType:TRTC.TYPE.STREAM_TYPE_SUB})
   * // get remote user main stream video frame
   * trtc.getVideoSnapshot({userId: 'remote userId', streamType:TRTC.TYPE.STREAM_TYPE_MAIN})
   * @memberof TRTC
   */
  @addAPICallLog()
  getVideoSnapshot(config: VideoFrameConfig = {}) {
    const { userId, streamType = TRTCStreamType.Main } = config;
    if (userId) {
      // 获取远端视频帧
      const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
      if (streamType === TRTCStreamType.Main && remotePublishedUser?.muteState.hasVideo) {
        return remotePublishedUser.remoteVideoTrack.getVideoFrame();
      }
      if (streamType === TRTCStreamType.Sub && remotePublishedUser?.muteState.hasAuxiliary) {
        return remotePublishedUser.remoteAuxiliaryTrack.getVideoFrame();
      }
    } else {
      // 获取本地视频帧
      if (streamType === TRTCStreamType.Main && this._localVideoTrack) {
        return this._localVideoTrack.getVideoFrame();
      }
      if (streamType === TRTCStreamType.Sub && this._localScreenTrack) {
        return this._localScreenTrack.getVideoFrame();
      }
    }
    return '';
  }

  @addAPICallLog()
  private _setCurrentSpeaker(speakerId: string) {
    this._speakerId = speakerId;
    this._localAudioTrack?.setAudioOutput(speakerId);
    this._localScreenAudioTrack?.setAudioOutput(speakerId);
    this._room.remotePublishedUserMap.forEach(remotePublishedUser => remotePublishedUser.remoteAudioTrack.setAudioOutput(speakerId));
  }

  async setCurrentSpeaker(speakerId: string) {
    const speakerList = await getSpeakers();
    speakerList.forEach(speaker => {
      if (speaker.deviceId === speakerId) {
        this._setCurrentSpeaker(speakerId);
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: speaker });
        lastSpeakerDeviceInfo = speaker;
      }
    });
    this._log.warn('the "setCurrentSpeaker" method of the instance will be deprecated in the future, please use "TRTC.setCurrentSpeaker" instead. For more information, please visit: https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#.setCurrentSpeaker');
  }

  @randStart((config: RemoteAudioConfig) => `a${config.userId}`, () => true)
  private _startRemoteAudio(config: RemoteAudioConfig) {
    return this._doStartRemoteAudio(config);
  }

  // 业务侧 muteRemoteAudio(false) 和 SDK 音频自动拉流都会调用该方法
  private async _doStartRemoteAudio(config: RemoteAudioConfig) {
    const { userId } = config;
    if (this._remoteAudioConfigMap.has(userId)) {
      this._log.warn(`remote audio has already started. userId:${userId}`);
      return;
    }
    const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
    if (!remotePublishedUser) return;
    const playOption: PlayOption = {};

    const remoteTrack = remotePublishedUser!.remoteAudioTrack;
    remoteTrack.on(RemoteTrackEvent.DECODE_FAILED, (error: any) => {
      this.emit(TRTCEvent.ERROR, new RtcError({
        code: ErrorCode.OPERATION_FAILED,
        extraCode: ErrorCodeDictionary.AUDIO_ENCODE_FAILED,
        message: 'audio decode failed'
      }));
    });
    this._listenOutputTrackChanged(remoteTrack);
    if (this._speakerId) remoteTrack.setAudioOutput(this._speakerId);

    try {
      const cachedVolume = this._remoteAudioVolumeMap.get(userId);
      const volume = isNumber(cachedVolume) ? cachedVolume : 100;
      playOption.volume = volume;
      this._remoteAudioConfigMap.set(userId, config);
      await this._room.subscribe(remoteTrack);
      pipe(fromEvent(remoteTrack, RemoteTrackEvent.DECODE_FAILED), takeUntil(fromEvent(remoteTrack, FSM.INIT)), subscribe(() => {
        this.startPlugin(AudioDecoderPlugin.Name, {
          track: remoteTrack,
          type: 'auto', config: {
            codec: 'opus',
            sampleRate: 48000,
            numberOfChannels: 1
          }
        });
      }));
      await this._updateAudioPlayOption({ playOption, track: remoteTrack });
      innerEmitter.emit(INNER_EVENT.REMOTE_AUDIO_STARTED, { userId, room: this.room });
      if (remoteTrack.outMediaTrack) {
        this.room.audioManager.updateAudioReference({ type: AudioReferenceChangeType.ADD, audioReference: remoteTrack.outMediaTrack, refId: `${AUDIO_REFERENCE_TYPE.REMOTE_AUDIO}-${userId}` });
      }
    } catch (error) {
      this._remoteAudioConfigMap.delete(userId);
      throw error;
    }
    this._emitTrackEvent(remoteTrack);
  }

  @createDecorator<TRTC>(next => async function (this: TRTC, config: StopRemoteAudioConfig) {
    if (config.userId === '*') {
      return Promise.all([...this._room.remotePublishedUserMap.values()].map(user => this._stopRemoteAudio({ ...config, userId: user.userId }).catch(() => { })));
    }
    return next.call(this, config);
  })
  @randStop((config: StopRemoteAudioConfig) => `a${config.userId}`)
  private async _stopRemoteAudio(config: StopRemoteAudioConfig, callUnsubscribe = true) {
    const remotePublishedUser = this._room.remotePublishedUserMap.get(config.userId);
    if (remotePublishedUser) {
      remotePublishedUser.remoteAudioTrack.stop();
      if (remotePublishedUser.muteState.hasAudio) {
        if (callUnsubscribe) {
          await this._room.unsubscribe(remotePublishedUser.remoteAudioTrack);
        }
      }
      this._mediaTrackMap.delete(remotePublishedUser.remoteAudioTrack.outMediaTrack!);
    }
    this._remoteAudioConfigMap.delete(`${config.userId}`);
    innerEmitter.emit(INNER_EVENT.REMOTE_AUDIO_STOPPED, { userId: config.userId, room: this.room });
    this.room.audioManager.updateAudioReference({ type: AudioReferenceChangeType.REMOVE, refId: `${AUDIO_REFERENCE_TYPE.REMOTE_AUDIO}-${config.userId}` });
  }
  private _enableVideoDecodeFallback(remoteTrack: RemoteVideoTrack, streamType: TRTCStreamType) {
    const fallback = this._room.videoDecodeFallbackType;
    if (!fallback || !this._plugins.has('TRTCVideoDecoder')) return;
    remoteTrack.log.debug('remote video will fall back when decode failed', remoteTrack.id);
    let fallbackState: VideoDecoderDowngradeState;
    pipe(fromEvent(remoteTrack, RemoteTrackEvent.DECODE_FAILED), takeUntil(fromEvent(remoteTrack, FSM.INIT)), tap(() => {
      if (this._room.downlinkVideoCodec === VideoCodec.H265) return;
      this.startPlugin('TRTCVideoDecoder', {
        type: 'auto',
        renderer: 'videoFrame',
        track: remoteTrack,
        config: {
          codec: 'avc1.420028'
        },
        fallback
      });
    }), switchMapTo(fromEvent(remoteTrack, RemoteTrackEvent.DECODE_DOWNGRADE_STATE_CHANGED)), subscribe((event: {
      type: string;
      renderer: string;
      reason: string;
      prevState: VideoDecoderDowngradeState;
      state: VideoDecoderDowngradeState;
    }) => {
      fallbackState = event.state;
      this.emit(TRTCEvent.VIDEO_DECODE_DOWNGRADE_STATE_CHANGED, {
        ...event,
        streamType,
        userId: remoteTrack.userId
      });
    }, err => {
      remoteTrack.log.error('fallback', err);
    }, () => {
      if (fallbackState === VideoDecoderDowngradeState.STARTED) remoteTrack.log.info('fallback complete');
    }));
  }
  // 更新 playOption
  private async _updateVideoPlayOption({
    view,
    playOption,
    track,
    prevConfig
  }: { view?: string | HTMLElement | HTMLElement[] | null, playOption: PlayOption, track: LocalVideoTrack | RemoteVideoTrack, prevConfig?: LocalVideoConfig | ScreenShareConfig | RemoteVideoConfig; }) {
    track.setMirror(playOption.mirror);
    // 这一次调用没有传 view 参数，则复用之前的 view，更新播放参数。
    if (isUndefined(view) && prevConfig && prevConfig.view && !isEmpty(playOption)) {
      const viewList = getViewListFromView(prevConfig.view);
      if (viewList.length > 0) {
        await track.play(viewList, playOption);
      }
    }
    // 这一次调用传了 view 参数
    if (!isUndefined(view)) {
      const viewList = getViewListFromView(view);
      if (viewList.length > 0) {
        await track.play(viewList, playOption);
      } else {
        track.stop();
      }
    }
  }

  private async _updateAudioPlayOption({
    playOption = {},
    track,
    prevConfig
  }: { playOption?: PlayOption, track: LocalAudioTrack | RemoteAudioTrack, prevConfig?: LocalAudioConfig | RemoteAudioConfig; }) {
    if (!track.isPlayCalled) {
      try {
        await track.play(null, playOption);
      } catch (error) {
        // 自动播放失败不抛出给接入侧，接入侧统一监听 AUTOPLAY_FAILED 处理。
      }
    }

    if (!isUndefined(playOption.muted)) {
      track.setPlayerMute(playOption.muted);
    }

    if (!isUndefined(playOption.volume)) {
      track.setAudioVolume(playOption.volume / 100);
    }

    // 耳返的回声消除
    if (track instanceof LocalAudioTrack && track.mediaTrack) {
      const changeType = (playOption.muted === false && !isUndefined(playOption.volume) && playOption.volume > 0) ? AudioReferenceChangeType.ADD : AudioReferenceChangeType.REMOVE;
      this.room.audioManager.updateAudioReference({ type: changeType, audioReference: track.mediaTrack, refId: `${AUDIO_REFERENCE_TYPE.EAR_MONITOR}` });
    } else if (track instanceof RemoteAudioTrack) {
      const volume = playOption.muted ? 0 : playOption.volume;
      if (isUndefined(volume)) return;
      this.room.audioManager.updateAudioReference({ type: AudioReferenceChangeType.UPDATE_VOLUME, refId: `${AUDIO_REFERENCE_TYPE.REMOTE_AUDIO}-${track.userId}`, volume: playOption.volume });
    }
  }

  private _listenOutputTrackChanged(track: ITrack) {
    if (track.listeners(TrackEvent.OUTPUT_MEDIA_TRACK_CHANGED).length === 0) {
      track.on(TrackEvent.OUTPUT_MEDIA_TRACK_CHANGED, () => this._emitTrackEvent(track, false));
    }
  }

  // 1. 经 setOutputMediaTrack 事件抛出，无需防重复检测
  // 2. 在订阅成功后，主动抛出事件，由于远端 track 可能已经提前经过 setOutputMediaTrack 抛出，此时需要做防重复检测，避免重复抛出相同 track
  private _emitTrackEvent(track: ITrack, uniqueCheck = true) {
    const userId = track.isRemote ? track.userId : '';
    if (!track.outMediaTrack) return;
    if (uniqueCheck && this._mediaTrackMap.get(track.outMediaTrack) === userId) return;
    this._mediaTrackMap.set(track.outMediaTrack, userId);
    this.emit(TRTCEvent.TRACK, { userId, streamType: convertStreamType(track.streamType), track: track.outMediaTrack, sourceTrack: track.mediaTrack! });
  }

  private _checkTrackToPublish() {
    const needPublishTrack: Array<ILocalTrack> = [];
    if (this._localAudioConfig?.publish && this._localAudioTrack) needPublishTrack.push(this._localAudioTrack);
    if (this._localVideoConfig?.publish && this._localVideoTrack) needPublishTrack.push(this._localVideoTrack);
    if (this._localScreenConfig?.publish) {
      if (this._localScreenTrack) {
        needPublishTrack.push(this._localScreenTrack);
      }
      if (this._localScreenAudioTrack) {
        needPublishTrack.push(this._localScreenAudioTrack);
      }
      this._checkScreenAudioEchoCancellation(this._localScreenTrack, this._localScreenAudioTrack);
    }
    if (needPublishTrack.length === 0) return;
    return this._room.publish(...needPublishTrack).catch(() => { });
  }

  /**
   * 监听 view 的可视状态变化。只订阅可视区域视频流，非可视区域的视频流取消订阅。
   *
   * 1. 支持监听多个 view 的可视状态变化。
   * 2. 支持防抖（快速来回滚动）(200ms)。
   * @private
   */
  private _observeView({ remoteTrack, view, receiveWhenViewVisible = false, viewRoot }: { remoteTrack: IRemoteTrack, view?: string | HTMLElement | HTMLElement[] | null, viewRoot?: HTMLElement, receiveWhenViewVisible?: boolean; }) {
    // 这次调用没有传 view 参数，则无需处理，保持原状。
    if (isUndefined(view)) return;

    const remoteVideoConfig = this._remoteVideoConfigMap.get(`${remoteTrack.userId}_${convertStreamType(remoteTrack.streamType)}`);
    if (!remoteVideoConfig) return;

    let observer: IntersectionObserver | undefined = remoteVideoConfig.observer || undefined;
    // 业务侧停止播放 view 时，不需要 observe
    const WITHOUT_VIEW = view === null || (isArray(view) && view.length === 0);
    if (WITHOUT_VIEW || !receiveWhenViewVisible) {
      observer?.disconnect();
      if (!remoteTrack.isSubscribed) {
        this._room.subscribe(remoteTrack).catch(() => { });
      }
      return;
    }
    const visibleViewMap = remoteVideoConfig.visibleViewMap || new Map();
    let observerTimeoutId = -1;
    if (!observer || observer.root !== viewRoot) {
      observer?.disconnect();
      visibleViewMap.clear();
      observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
          visibleViewMap.set(entry.target, entry.isIntersecting);

          remoteTrack.log.info(`view ${entry.target.id} is${entry.isIntersecting ? '' : ' not'} visible`);
        });

        clearTimeout(observerTimeoutId);
        // 防抖
        observerTimeoutId = window.setTimeout(() => {
          // 一个可视的 view 都找不到，则停止拉流
          const isAnyViewVisible = [...visibleViewMap.values()].find(visible => visible);
          if (isAnyViewVisible) {
            if (!remoteTrack.isSubscribed) {
              this._room.subscribe(remoteTrack).catch(() => { });
            }
          } else {
            if (remoteTrack.isSubscribed) {
              this._room.unsubscribe(remoteTrack).catch(() => { });
            }
          }
        }, 200);
      }, { root: viewRoot });
    }

    const currentViewSet = new Set(getViewListFromView(view));

    // 不需要播放渲染的 view，及时 unobserve。
    visibleViewMap.forEach((visible, view) => {
      if (!currentViewSet.has(view)) {
        observer!.unobserve(view);
        visibleViewMap.delete(view);
      }
    });

    // 默认用户传入的 view 都是可视的
    currentViewSet.forEach(view => {
      visibleViewMap.set(view, true);
      observer!.observe(view);
    });
    // 手动更新一次 view 列表的可视状态。
    observer.takeRecords().forEach(entry => {
      visibleViewMap.set(entry.target, entry.isIntersecting);
    });
    remoteVideoConfig.visibleViewMap = visibleViewMap;
    remoteVideoConfig.observer = observer;
  }

  @randStop('room')
  private async _exitRoom() {
    if (this._room.isJoined) {
      await this._room.leave();
    }
    new Set([...this._remoteAudioConfigMap.keys(), ...this._remoteAudioMuteMap.keys()]).forEach(userId => {
      this._stopRemoteAudio({ userId }).catch();
    });
    [...this._remoteVideoConfigMap.keys()].forEach(key => {
      const streamType = key.includes(TRTCStreamType.Main) ? TRTCStreamType.Main : TRTCStreamType.Sub;
      const userId = key.split(`_${streamType}`)[0];
      if (userId) {
        this._stopRemoteVideo({ userId, streamType }).catch();
      }
    });
    this._remoteVideoConfigMap.clear();
    this._remoteAudioConfigMap.clear();
    this._remoteAudioMuteMap.clear();
    this._remoteAudioVolumeMap.clear();
    clearDebounceCache(this);
    this._room.remotePublishedUserMap.forEach(remotePublisherUser => {
      eventManager.remove(remotePublisherUser.remoteAudioTrack);
      eventManager.remove(remotePublisherUser.remoteVideoTrack);
      eventManager.remove(remotePublisherUser.remoteAuxiliaryTrack);
    });
  }

  @randStop('screen')
  private async _stopScreenShare() {
    if (!this._localScreenTrack) return;

    if (this._room.isJoined) {
      const trackListToUnpublish: ILocalTrack[] = [this._localScreenTrack];
      if (this._localScreenAudioTrack) trackListToUnpublish.push(this._localScreenAudioTrack);
      // unpublish 不会失败，catch error
      await this._room?.unpublish(...trackListToUnpublish).catch(() => { });
    }
    this._localScreenTrack.stop();
    this._localScreenTrack.close();
    if (this._localScreenAudioTrack) {
      if (this._localScreenAudioTrack.trackSettings?.echoCancellation === false) {
        this.stopPlugin('AudioProcessor');
      }
      this._localScreenAudioTrack.stop();
      this._localScreenAudioTrack.close();
      this._room.audioManager.removeInput(this._localScreenAudioTrack);
      this._localScreenAudioTrack = null;
    }
    eventManager.remove(this._localScreenTrack);
    this._localScreenTrack = null;
    this._localScreenConfig = null;
  }

  // chrome 137+ 屏幕分享回声消除未生效, 开启自研 AEC 规避. ref: https://issues.chromium.org/issues/422611724
  private async _checkScreenAudioEchoCancellation(screenTrack?: LocalScreenTrack | null, screenAudioTrack?: LocalScreenAudioTrack | null) {
    if (!screenTrack || !screenAudioTrack) return;
    const displaySurface = screenTrack.trackSettings?.displaySurface;
    // 条件: 1. 回声消除未开启 2. 屏幕分享是整个屏幕 3. 屏幕分享是当前 tab 页
    if (screenAudioTrack.trackSettings?.echoCancellation === false && (displaySurface === 'monitor' || (displaySurface === 'browser' && screenTrack.isShareCurrentTab))) {
      this._log.warn('echoCancellation of screen audio track is disable. Try starting audioProcessor plugin');
      try {
        await this.startPlugin('AudioProcessor' as keyof PluginStartOptionsMap, { sdkAppId: Number(this.room.sdkAppId), userId: this._room.userId, userSig: this.room.userSig, isScreenAudioNeedAudioProcess: true, isLocalAudioNeedAudioProcess: false });
      } catch (error) {
        this._log.warn('start audioProcessor plugin failed: ', error);
      }
    }
  }

  private _onLocalTrackCaptured({ track }: InnerEventTypes[INNER_EVENT.LOCAL_TRACK_CAPTURE_SUCCESS][0]) {
    if (track.kind === 'audio' && (!lastSpeakerDeviceInfo || isDeviceGotFromWithoutPermission(lastSpeakerDeviceInfo))) {
      this._initActiveSpeaker();
      innerEmitter.off(INNER_EVENT.LOCAL_TRACK_CAPTURE_SUCCESS, this._onLocalTrackCaptured, this);
    }
  }
  private async _initActiveSpeaker() {
    if (lastSpeakerDeviceInfo && !isDeviceGotFromWithoutPermission(lastSpeakerDeviceInfo)) {
      this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: lastSpeakerDeviceInfo });
    } else {
      // 初始化默认扬声器
      const speakers = await getSpeakers();
      if (speakers[0] && !isDeviceGotFromWithoutPermission(speakers[0])) {
        lastSpeakerDeviceInfo = speakers[0];
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: speakers[0] });
      } else {
        innerEmitter.on(INNER_EVENT.LOCAL_TRACK_CAPTURE_SUCCESS, this._onLocalTrackCaptured, this);
      }
    }
  }

  private _onAudioAvailable({ userId }: { userId: string; }) {
    const mutedRemote = this._remoteAudioMuteMap.has(userId) ? this._remoteAudioMuteMap.get(userId) : this._remoteAudioMuteMap.get('*');
    // 1. 用户调用 muteRemoteAudio(userId, false) 取消静音，相当于开启自动拉流。
    // 2. 开了自动拉流 && 用户未调用 muteRemoteAudio(userId, true) 静音远端
    if (mutedRemote === false || (this._room.autoReceiveAudio && !mutedRemote)) {
      this._doStartRemoteAudio({ userId }).catch(() => { });
    }
  }
  private _onVideoAvailable({ userId, streamType }: { userId: string, streamType: TRTCStreamType; }) {
    if (!this._room.autoReceiveVideo) return;
    const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
    if (remotePublishedUser) {
      const remoteTrack = streamType === TRTCStreamType.Main ? remotePublishedUser.remoteVideoTrack : remotePublishedUser.remoteAuxiliaryTrack;
      const remoteTrackToSubscribe: IRemoteTrack[] = [remoteTrack];
      // 同时订阅音频 + 视频，加快秒开时间。
      if (this._room.autoReceiveAudio && remotePublishedUser.remoteAudioTrack.isAvailable) remoteTrackToSubscribe.push(remotePublishedUser.remoteAudioTrack);
      this._room.subscribe(...remoteTrackToSubscribe)
        .then(() => {
          this._emitTrackEvent(remoteTrack);
        })
        .catch(() => { });
    }
  }
  private _onAudioUnavailable({ userId, muteState }: { userId: string, muteState: MuteState; }) {
    // remote muted audio 情况下，不需要调用 stopRemoteAudio。因为 mute unmute 是个频繁的操作，
    // 如果调用 stopRemoteAudio 可能会导致远端 unmute 的时候，重新订阅耗时增加导致丢音。
    // 另外在 iOS 14 13 版本，取消订阅音频会导致 audioTrack ended，后续重新订阅会听不到声音。
    if (muteState.hasAudio && muteState.audioMuted) return;
    this._stopRemoteAudio({ userId }, false).catch(() => { });
  }
  private _onVideoUnavailable({ userId, streamType }: { userId: string, streamType: TRTCStreamType; }) {
    // SDK 自动清理相关状态，让业务侧在后续 available 时可以 startRemoteVideo
    // 无需调用 unsubscribe 接口
    this._stopRemoteVideo({ userId, streamType }, false).catch(() => { });
  }
  /**
   * Send SEI Message <br>
   *
   * > The header of a video frame has a header block called SEI.
   * > The principle of this interface is to use the SEI to embed the custom data you want to send along with the video frame.
   * > SEI messages can accompany video frames all the way to the live CDN.
   *
   * Applicable scenarios: synchronization of lyrics, live answering questions, etc.
   *
   * When to call: call after {@link TRTC#startLocalVideo trtc.startLocalVideo} or {@link TRTC#startLocalScreen trtc.startLocalScreen} when set 'toSubStream' option to true successfully.
   *
   * Note:
   * 1. Maximum 1KB(Byte) sent in a single call, maximum 30 calls per second, maximum 8KB sent per second.
   * 2. Supported browsers: Chrome 86+, Edge 86+, Opera 72+, Safari 15.4+, Firefox 117+. Safari and Firefox are supported since v5.8.0.
   * 3. Since SEI is sent along with video frames, there is a possibility that video frames may be lost, and therefore SEI may be lost as well. The number of times it can be sent can be increased within the frequency limit, and the business side needs to do message de-duplication on the receiving side.
   * 4. SEI cannot be sent without trtc.startLocalVideo(or trtc.startLocalScreen when set 'toSubStream' option to true); SEI cannot be received without startRemoteVideo.
   * 5. Only H264 encoder is supported to send SEI.
   * @see {@link module:EVENT.SEI_MESSAGE TRTC.EVENT.SEI_MESSAGE}
   * @since v5.3.0
   * @param {ArrayBuffer} buffer SEI data to be sent
   * @param {Object=} options
   * @param {Number} options.seiPayloadType Set the SEI payload type. SDK uses the custom payloadType 243 by default, the business side can use this parameter to set the payloadType to the standard 5. When the business side uses the 5 payloadType, you need to follow the specification to make sure that the first 16 bytes of the `buffer` are the business side's customized uuid.
   * @param {Boolean} [options.toSubStream=false] Send SEI data to substream. Need call trtc.startLocalScreen first. Since v5.7.0+.
   * @example
   * // 1. enable SEI
   * const trtc = TRTC.create({
   *    enableSEI: true
   * })
   *
   * // 2. send SEI
   * try {
   *  await trtc.enterRoom({
   *   userId: 'user_1',
   *   roomId: 12345,
   * })
   *  await trtc.startLocalVideo();
   *  const unit8Array = new Uint8Array([1, 2, 3]);
   *  trtc.sendSEIMessage(unit8Array.buffer);
   * } catch(error) {
   *  console.warn(error);
   * }
   *
   * // 3. receive SEI
   * trtc.on(TRTC.EVENT.SEI_MESSAGE, event => {
   *  console.warn(`sei ${event.data} from ${event.userId}`);
   * })
   */
  @validate(...validateConfig.TRTC.sendSEIMessage)
  @limitCallFrequency({
    timesInSecond: 30,
    maxSizeInSecond: 8000,
    getSize: (...args) => args[0].byteLength
  })
  sendSEIMessage(buffer: ArrayBuffer, options?: { seiPayloadType?: number; toSubStream?: boolean; }) {
    const seiPlugin = this._plugins.get('SEI');
    if (seiPlugin) {
      seiPlugin.update({ buffer, options: { seiPayloadType: 243, ...options, small: !!this._localVideoTrack?.small } });
      kvStatManager.addCount({ key: KV_REPORT_KEY_API.sendSEIMessage, useUV: true });
    }
  }

  /**
   * Send Custom Message to all remote users in the room. <br>
   *
   * Note:
   *
   * 1. Only {@link module:TYPE.ROLE_ANCHOR TRTC.TYPE.ROLE_ANCHOR} can call sendCustomMessage.
   * 2. You should call this api after {@link TRTC#enterRoom TRTC.enterRoom} successfully.
   * 3. The custom message will be sent in order and as reliably as possible, but it's possible to loss messages in a very bad network. The receiver will also receive the message in order.
   * @since v5.6.0
   * @see Listen for the event {@link module:EVENT.CUSTOM_MESSAGE TRTC.EVENT.CUSTOM_MESSAGE} to receive custom message.
   * @param {object} message
   * @param {number} message.cmdId message Id. Integer, range [1, 10]. You can set different cmdId for different types of messages to reduce the delay of transferring message.
   * @param {ArrayBuffer} message.data - message content. <br/>
   * - Maximum 1KB(Byte) sent in a single call.
   * - Maximum 30 calls per second
   * - Maximum 8KB sent per second.
   * @example
   * // send custom message
   * trtc.sendCustomMessage({
   *   cmdId: 1,
   *   data: new TextEncoder().encode('hello').buffer
   * });
   *
   * // receive custom message
   * trtc.on(TRTC.EVENT.CUSTOM_MESSAGE, event => {
   *    // event.userId: remote userId.
   *    // event.cmdId: message cmdId.
   *    // event.seq: message sequence number.
   *    // event.data: custom message data, type is ArrayBuffer.
   *    console.log(`received custom msg from ${event.userId}, message: ${new TextDecoder().decode(event.data)}`)
   * })
   */
  @validate(validateConfig.TRTC.sendCustomMessage)
  @limitCallFrequency({
    timesInSecond: 30,
    maxSizeInSecond: 8000,
    getSize: (data: CustomMessageData) => data.data.byteLength
  })
  sendCustomMessage(message: CustomMessageData) {
    this._room.sendCustomMessage?.(message);
    kvStatManager.addCount({ key: KV_REPORT_KEY_API.sendCustomMessage, useUV: true });
  }

  callExperimentalAPI<T extends keyof ExperimentalAPIFunctionMap, O extends ExperimentalAPIFunctionMap[T]>(name: O extends undefined ? never : T, options: O): Promise<void>;
  callExperimentalAPI<T extends keyof ExperimentalAPIFunctionMap, O extends ExperimentalAPIFunctionMap[T]>(name: O extends undefined ? T : never): Promise<void>;
  /**
   * call experimental API
   *
   * | APIName | name | param |
   * | --- | --- | --- |
   * | 'enableAudioFrameEvent' | Config the pcm data of Audio Frame Event | [EnableAudioFrameEventOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#EnableAudioFrameEventOptions) |
   * @param {string} name
   * @param {EnableAudioFrameEventOptions} options
   * @example
   * // Call back the pcm data of the remote user 'user_A'. The default pcm data is 48kHZ, mono
   * await trtc.callExperimentalAPI('enableAudioFrameEvent', { enable: true, userId: 'user_A'})
   * // Call back all remote pcm data and set the pcm data to 16kHZ, stereo
   * await trtc.callExperimentalAPI('enableAudioFrameEvent', { enable: true, userId: '*', sampleRate: 16000, channelCount: 2 })
   * // Set the MessagePort for the local microphone pcm data callback
   * await trtc.callExperimentalAPI('enableAudioFrameEvent', { enable: true, userId: '', port })
   * // Cancel callback of local microphone pcm data
   * await trtc.callExperimentalAPI('enableAudioFrameEvent', { enable: false, userId: '' })
   */
  async callExperimentalAPI<T extends keyof ExperimentalAPIFunctionMap>(name: T, options?: ExperimentalAPIFunctionMap[T]) {
    this._log.info(`callExperimentalAPI(${name}, ${JSON.stringify(options)})`);
    return experimentalApi.call<T>(name, { trtcInstance: this, ...options as ExperimentalAPIFunctionMap[T] });
  }

  static EVENT = TRTCEvent;
  static ERROR_CODE = ErrorCode;
  static TYPE = TRTCType;
  static frameWorkType = 30;

  /**
   * Set the log output level
   * <br>
   * It is recommended to set the DEBUG level during development and testing, which includes detailed prompt information.
   * The default output level is INFO, which includes the log information of the main functions of the SDK.
   *
   * @param {0-5} [level] Log output level 0: TRACE 1: DEBUG 2: INFO 3: WARN 4: ERROR 5: NONE
   * @param {boolean} [enableUploadLog=true] Whether to enable log upload, which is enabled by default. It is not recommended to turn it off, which will affect problem troubleshooting.
   * @example
   * // Output log levels above DEBUG
   * TRTC.setLogLevel(1);
   */
  static setLogLevel(level: LOG_LEVEL, enableUploadLog?: boolean) {
    loggerManager.setLogLevel(level);
    if (!isUndefined(enableUploadLog)) {
      enableUploadLog ? loggerManager.enableUploadLog() : loggerManager.disableUploadLog();
    }
  }

  /**
   * Check if the TRTC Web SDK is supported by the current browser
   *
   * - Reference: {@tutorial 05-info-browser}.
   * @example
   * TRTC.isSupported().then((checkResult) => {
   *   if(!checkResult.result) {
   *      console.log('checkResult', checkResult.result, 'checkDetail', checkResult.detail);
   *      // The SDK does not support the current browser. Guide the user to use the latest version of Chrome.
   *   }
   *   const { isBrowserSupported, isWebRTCSupported, isMediaDevicesSupported, isH264EncodeSupported, isH264DecodeSupported } = checkResult.detail;
   *
   *   // Different business scenarios may require varying levels of detection granularity, such as only needing pushing or pulling stream.
   *   // Detect whether the browser supports capturing the camera and microphone for upstreaming.
   *   const isPushMicrophoneSupported = isBrowserSupported && isWebRTCSupported && isMediaDevicesSupported;
   *   const isPushCameraSupported = isBrowserSupported && isWebRTCSupported && isMediaDevicesSupported && isH264EncodeSupported;
   *
   *   // Detect whether the browser supports pulling stream.
   *   const isPullAudioSupported = isBrowserSupported && isWebRTCSupported;
   *   const isPullVideoSupported = isBrowserSupported && isWebRTCSupported && isH264DecodeSupported;
   *});
   *
   * @returns {Promise.<object>} Promise returns the detection result
   * | Property                                   | Type    | Description                         |
   * |--------------------------------------------|---------|-------------------------------------|
   * | checkResult.result                         | boolean | If true, it means the current environment supports the basic ability to capture the camera and microphone for streaming.                             |
   * | checkResult.detail.isBrowserSupported      | boolean | Whether the current browser is supported by the SDK        |
   * | checkResult.detail.isWebRTCSupported       | boolean | Whether the current browser supports WebRTC               |
   * | checkResult.detail.isWebCodecsSupported    | boolean | Whether the current browser supports WebCodecs            |
   * | checkResult.detail.isMediaDevicesSupported | boolean | Whether the current browser supports accessing media devices and capturing microphone/camera     |
   * | checkResult.detail.isScreenShareSupported | boolean | Whether the current browser supports screen sharing functionality   |
   * | checkResult.detail.isSmallStreamSupported | boolean | Whether the current browser supports small streams     |
   * | checkResult.detail.isH264EncodeSupported   | boolean | Whether the current browser supports H264 encoding        |
   * | checkResult.detail.isH264DecodeSupported   | boolean | Whether the current browser supports H264 decoding         |
   * | checkResult.detail.isVp8EncodeSupported    | boolean | Whether the current browser supports VP8 encoding          |
   * | checkResult.detail.isVp8DecodeSupported    | boolean | Whether the current browser supports VP8 decoding          |
   */
  static isSupported() {
    return checkSystemRequirementsInternal(TRTC.frameWorkType);
  }

  /**
   * Returns the list of camera devices
   * <br>
   * **Note**
   * - This interface does not support use under the http protocol, please use the https protocol to deploy your website. {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Privacy_and_security Privacy and security}
   * - You can call the browser's native interface [getCapabilities](https://developer.mozilla.org/en-US/docs/Web/API/InputDeviceInfo/getCapabilities) to get the maximum resolutions supported by the camera, frame rate, and distinguish between front and rear cameras on mobile devices, etc. This interface supports Chrome 67+, Edge 79+, Safari 17+, Opera 54+.
   *   - On Huawei phones, the rear camera may capture the telephoto lens. In this case, you can use this interface to obtain the rear camera with the largest supported resolution. Generally, the main camera of a phone has the largest resolution.
   * @param {boolean} [requestPermission=true] `Since v5.6.3`. Whether to request permission to use the camera. If requestPermission is true, calling this method may temporarily open the camera to ensure that the camera list can be normally obtained, and the SDK will automatically stop the camera capture later.
   * @example
   * const cameraList = await TRTC.getCameraList();
   * if (cameraList[0] && cameraList[0].getCapabilities) {
   *   const { width, height, frameRate, facingMode } = cameraList[0].getCapabilities();
   *   console.log(width.max, height.max, frameRate.max);
   *   if (facingMode) {
   *     if (facingMode[0] === 'user') {
   *       // front camera
   *     } else if (facingMode[0] === 'environment') {
   *       // rear camera
   *     }
   *   }
   * }
   * @returns {Promise.<MediaDeviceInfo[]>} Promise returns an array of {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo|MediaDeviceInfo}
   */
  static getCameraList(requestPermission = true) {
    return getCameras(requestPermission);
  }

  /**
   * Returns the list of microphone devices
   * <br>
   * **Note**
   * - This interface does not support use under the http protocol, please use the https protocol to deploy your website. {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Privacy_and_security Privacy and security}
   * - You can call the browser's native interface [getCapabilities](https://developer.mozilla.org/en-US/docs/Web/API/InputDeviceInfo/getCapabilities) to get information about the microphone's capabilities, e.g. the maximum number of channels supported, etc. This interface supports Chrome 67+, Edge 79+, Safari 17+, Opera 54+.
   * - On Android, there are usually multiple microphones, and the label list is: ['default', 'Speakerphone', 'Headset earpiece'], if you do not specify the microphone in trtc.startLocalAudio, the browser default microphone may be the Headset earpiece and the sound will come out of the headset. If you need to play out through the speaker, you need to specify the microphone with the label 'Speakerphone'.
   * @param {boolean} [requestPermission=true] `Since v5.6.3`. Whether to request permission to use the microphone. If requestPermission is true, calling this method may temporarily open the microphone to ensure that the microphone list can be normally obtained, and the SDK will automatically stop the microphone capture later.
   * @example
   * const microphoneList = await TRTC.getMicrophoneList();
   * if (microphoneList[0] && microphoneList[0].getCapabilities) {
   *   const { channelCount } = microphoneList[0].getCapabilities();
   *   console.log(channelCount.max);
   * }
   * @returns {Promise.<MediaDeviceInfo[]>} Promise returns an array of {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo|MediaDeviceInfo}
   */
  static getMicrophoneList(requestPermission = true) {
    return getMicrophones(requestPermission);
  }

  /**
   * Returns the list of speaker devices. Only support PC browser, not support mobile browser.
   * <br>
   * @param {boolean} [requestPermission=true] `Since v5.6.3`. Whether to request permission to use the microphone. If requestPermission is true, calling this method may temporarily open the microphone to ensure that the microphone list can be normally obtained, and the SDK will automatically stop the microphone capture later.
   * @returns {Promise.<MediaDeviceInfo[]>} Promise returns an array of {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo|MediaDeviceInfo}
   */
  static getSpeakerList(requestPermission = true) {
    return getSpeakers(requestPermission);
  }

  /**
   *  Set the current speaker for audio playback
   *
   *  **Note**
   *  - This interface only supports PC and Android devices.
   *  - Android device requirements:
   *    - SDK version >= 5.9.0
   *    - Support switching between speaker and headset, i.e. pass in TRTC.TYPE.SPEAKER or TRTC.TYPE.HEADSET
   *    - Need to call {@link TRTC#startLocalAudio startLocalAudio()} to collect microphone first
   *
   * @param {string | TRTC.TYPE.SPEAKER | TRTC.TYPE.HEADSET} speakerId Speaker ID, support passing in speaker ID or TRTC.TYPE.SPEAKER or TRTC.TYPE.HEADSET
   * @example
   * // For PC
   * TRTC.setCurrentSpeaker('your_expected_speaker_id');
   *
   * // For Android（sdk version >= 5.9.0）
   * TRTC.setCurrentSpeaker(TRTC.TYPE.SPEAKER); // Switch to speaker
   * TRTC.setCurrentSpeaker(TRTC.TYPE.HEADSET); // Or switch to headset
   */
  static async setCurrentSpeaker(speakerId: string) {
    // 安卓设备通过切换采集的麦克风实现扬声器的切换
    // known issue: 插入有线/蓝牙耳机, 在扬声器和听筒之间切换时, 音频会有一瞬间从耳机播放
    if (IS_ANDROID && (speakerId === TRTCType.SPEAKER || speakerId === TRTCType.HEADSET)) {
      // 安卓设备传入的 speakerId 是 microphone label
      const microphoneList = await TRTC.getMicrophoneList();
      let realSpeakerId = '';
      microphoneList.forEach(item => {
        if (item.label === speakerId) {
          realSpeakerId = item.deviceId;
        }
      });
      if (!realSpeakerId) return;
      trtcInstanceSet.forEach(async trtc => {
        if (trtc._localAudioTrack) {
          await trtc.updateLocalAudio({ option: { microphoneId: realSpeakerId } });
        }
      });
      return;
    }
    const speakerList = await getSpeakers();
    speakerList.forEach(speaker => {
      if (speaker.deviceId === speakerId) {
        trtcInstanceSet.forEach(trtc => {
          trtc._setCurrentSpeaker(speakerId);
          trtc.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: speaker });
        });
        lastSpeakerDeviceInfo = speaker;
      }
    });
  }

  /**
   * 添加 KV stat
   * @private
   * @param {object} param
   * @param {'count' | 'enum' | 'number'} param.type 统计类型
   * @param {number} param.key
   * @param {any} param.value
   * @param {number|number[]} [param.base = 100] 拆分粒度，默认 100
   * @param {boolean} [param.useUV] 是否使用 UV，对于 enum 默认是 true
   * @param {string} [param.version] 版本号
   * @param {number} [max = 5000] value 最大值，默认值 5000，防止数值过大，后台落库太多的 key。用于耗时上报时，则默认耗时最大 5000ms，超过这个数值一般没有统计意义了。
   */
  private static _addKVStat({ type, key, value, base, useUV, version, max }: { type: KV_STAT_TYPE, key: number, value: any, base?: number | number[], useUV: boolean, version?: string; max?: number; }) {
    if (version) {
      outterKVStatManager.version = version;
    }
    switch (type) {
      case KV_STAT_TYPE.COUNT:
        outterKVStatManager.addCount({ key, useUV });
        break;
      case KV_STAT_TYPE.ENUM:
        outterKVStatManager.addEnum({ key, value, useUV });
        break;
      case KV_STAT_TYPE.NUMBER:
        outterKVStatManager.addNumber({ key, value, split: base, max });
        break;
    }
  }
}

export default TRTC;
