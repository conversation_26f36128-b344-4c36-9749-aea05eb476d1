import { createDecorator, ProcessHandler } from 'trtc-js-sdk-core/src/utils/decorators/middleware';
import { getMediaStreamTrackInfo, getValueType, isPromise, performanceNow } from 'trtc-js-sdk-core/src/utils/utils';
import RtcError from '../common/rtc-error';
import TRTC from '../main';
import { loggerManager } from 'trtc-js-sdk-core';
import kvStatManager, { KV_REPORT_KEY, KV_REPORT_KEY_API } from 'trtc-js-sdk-core/src/manager/kv-stat-manager';

interface ADD_API_LOG_OPTIONS {
  getRemoteId?: (...args: any) => string;
  replaceArg?: (...args: any) => { argIndex: number, value: any }
  getKVReportKey?: (...args: any) => KV_REPORT_KEY;
  ignoreLog?: (...args: any) => boolean;
  ignoreErrorLog?: (...args: any) => boolean;
}
/**
 * 给 API 添加调用日志
 * @example
 * class TRTC {
 *  // 异步方法，在 startLocalVideo 开始、成功、失败时间点打印日志
 *  @addAPICallLog
 *  async startLocalVideo() {}
 *  // 同步方法，只在调用时打印日志
 *  @addAPICallLog
 *  stopLocalVideo() {}
 * }
 */
export function addAPICallLog(options: ADD_API_LOG_OPTIONS = {}) {
  const { getRemoteId = () => '', replaceArg, getKVReportKey, ignoreLog, ignoreErrorLog } = options;
  return createDecorator((next: ProcessHandler, name: string) => function (this: TRTC, ...args: any[]) {
    function stringifyParam(key: string, value: any, keysToHideValue?: string[]) {
      if (keysToHideValue && keysToHideValue.includes(key)) return 'hided';
      if (replaceArg) {
        const result = replaceArg(...args);
        if (args[result.argIndex] === value) return result.value;
      }
      if (value === args || key in args) return value;
      try {
        // 输出参数时，对于 HTMLElement 类型的参数，输出 elementId + element 类型
        if (value instanceof HTMLElement) {
          return `id: ${value.id} type:${getValueType(value)}`;
        }
        if (value instanceof MediaStreamTrack) {
          return getMediaStreamTrackInfo(value);
        }
        JSON.stringify(value);
        return value;
      } catch (error) {
        // 不可序列化参数返回参数类型，避免报错导致接口调用失败。例如循环引用对象无法序列化
        return `type:${getValueType(value)}`;
      }
    }
    // @ts-ignore
    const log = this._log || loggerManager;
    // 某些 API 调用不需要打印
    if (ignoreLog?.(...args)) return next.apply(this, args);
    if (args.length > 0) {
      // 打印参数时，隐藏部分敏感、过长的参数，当失败时，再完整打印。减少日志量。
      log.info(`${name}() ${JSON.stringify(args, (key, value) => stringifyParam(key, value, ['userSig', 'privateMapKey']))}`);
    } else {
      log.info(`${name}()`);
    }
    const kvReportKey = getKVReportKey ? getKVReportKey(...args) : KV_REPORT_KEY_API[name as keyof typeof KV_REPORT_KEY_API];
    const isIgnoreErrorLog = ignoreErrorLog?.(...args) || false;
    try {
      const now = performanceNow();
      const res = next.apply(this, args);

      (window as any).__TRTC_DEVTOOLS_DATA__[`t${this.seq}`].functions.push({
        name,
        startTimeStamp: now,
        endTimeStamp: Date.now(),
        params: args,
        sync: !isPromise(res),
        error: null
      });

      if (isPromise(res)) {
        const pluginLog = `${name.includes('Plugin') ? `${args[0].getName?.() || ''} ` : ' '}`;
        return res.then(val => {
          // @ts-ignore
          const devtoolsFunctionIndex = window.__TRTC_DEVTOOLS_DATA__[`t${this.seq}`].functions.findIndex(item => item.startTimeStamp === now);
          (window as any).__TRTC_DEVTOOLS_DATA__[`t${this.seq}`].functions[devtoolsFunctionIndex].endTimeStamp = Date.now();
          log.info(`${name}() success ${pluginLog}${getRemoteId.call(this, ...args)}`);
          kvStatManager.addSuccessEvent({ key: kvReportKey, cost: performanceNow() - now });
          return val;
        }).catch(error => {
          error = RtcError.convertFrom.call(this, error, name, args.length === 1 ? args[0] : args);
          const errorCode = error.extraCode || error.code;
          const errorCodeLog = error.message?.includes(errorCode) ? '' : ` code:${errorCode}`;
          if (!isIgnoreErrorLog) log.error(`${name}() failed ${pluginLog}${getRemoteId.call(this, ...args)} ${error}${errorCodeLog} params: ${JSON.stringify(args, stringifyParam)}`);
          kvStatManager.addFailedEvent({ key: kvReportKey, error });
          throw error;
        });
      }
      kvStatManager.addSuccessEvent({ key: kvReportKey });
      return res;
    } catch (error: any) {
      error = RtcError.convertFrom.call(this, error as Error, name);
      // 失败时打印完整参数
      const errorCode = error.extraCode || error.code;
      const errorCodeLog = error.message?.includes(errorCode) ? '' : ` code:${errorCode}`;
      const logString = `${name}() failed ${error}${errorCodeLog} params: ${JSON.stringify(args, stringifyParam)}`;
      if (!isIgnoreErrorLog) log.error(logString);
      (window as any).__TRTC_DEVTOOLS_DATA__[`t${this.seq}`].functions.find(item => item.startTimeStamp === now).error = logString;
      kvStatManager.addFailedEvent({ key: kvReportKey, error: error as RtcError });
      throw error;
    }
  });
}

